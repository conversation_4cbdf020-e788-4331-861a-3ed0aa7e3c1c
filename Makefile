# Arroyo University - Development Makefile

.PHONY: help build up down logs clean test lint format install-deps backup restore validate

# Configuration for smart validation
VALIDATE_ON_START ?= false
QUICK_VALIDATE ?= true
VERBOSE_TESTS ?= false
SILENT_MODE ?= false
CONTINUE_ON_ERROR ?= true

# Default target
help:
	@echo "Arroyo University - Available Commands:"
	@echo ""
	@echo "Development:"
	@echo "  make dev-setup       - Run automated development setup"
	@echo "  make up              - Start all services in development mode"
	@echo "  make dev             - Start services (alias for up)"
	@echo "  make dev-with-tests  - Start services with automatic validation"
	@echo "  make down            - Stop all services"
	@echo "  make build           - Build all Docker images"
	@echo "  make logs            - Show logs from all services"
	@echo "  make logs-follow     - Follow logs from all services"
	@echo "  make restart         - Restart all services"
	@echo ""
	@echo "Validation & Testing:"
	@echo "  make validate        - Quick environment validation"
	@echo "  make validate-full   - Comprehensive validation"
	@echo "  make test-sprint1    - Run Sprint 1 test suite"
	@echo "  make test-quick      - Run quick acceptance tests"
	@echo ""
	@echo "Production:"
	@echo "  make up-prod         - Start all services in production mode"
	@echo "  make down-prod       - Stop production services"
	@echo "  make build-prod      - Build production Docker images"
	@echo ""
	@echo "Database:"
	@echo "  make db-migrate      - Run database migrations"
	@echo "  make db-seed         - Seed database with sample data"
	@echo "  make db-backup       - Create database backup"
	@echo "  make db-restore      - Restore database from backup"
	@echo "  make db-reset        - Reset database (WARNING: destroys data)"
	@echo ""
	@echo "Testing:"
	@echo "  make test            - Run all tests"
	@echo "  make test-core       - Run core API tests"
	@echo "  make test-ai         - Run AI service tests"
	@echo "  make test-frontend   - Run frontend tests"
	@echo "  make test-coverage   - Run tests with coverage report"
	@echo ""
	@echo "Code Quality:"
	@echo "  make lint            - Run linting on all services"
	@echo "  make format          - Format code in all services"
	@echo "  make security-scan   - Run security scans"
	@echo ""
	@echo "Utilities:"
	@echo "  make clean           - Clean up Docker resources"
	@echo "  make install-deps    - Install development dependencies"
	@echo "  make status          - Show status of all services"
	@echo "  make shell-core      - Open shell in core API container"
	@echo "  make shell-ai        - Open shell in AI service container"
	@echo "  make shell-db        - Open PostgreSQL shell"

# Development commands
up:
	@echo "🚀 Starting development environment..."
	@docker-compose up -d
	@echo "⏳ Waiting for services to be ready..."
	@timeout /t 10 /nobreak > nul 2>&1 || sleep 10
	@echo ""
	@echo "✅ Services started successfully!"
	@echo "📊 Access URLs:"
	@echo "  Frontend: http://localhost:3000"
	@echo "  API Gateway: http://localhost:8080"
	@echo "  Core API: http://localhost:8000"
	@echo "  API Docs: http://localhost:8000/docs"
	@echo "  Health Check: http://localhost:8000/health"
	@echo "  Grafana: http://localhost:3001"
	@echo ""
	@echo "🔧 Useful commands:"
	@echo "  make logs-follow     - Follow service logs"
	@echo "  make validate        - Check system health"
	@echo "  make status          - Show service status"

# Alias for up command
dev: up

# Start with comprehensive validation
dev-with-tests:
	@echo "🚀 Starting development environment with validation..."
	@docker-compose up -d
	@echo "⏳ Waiting for services to be ready..."
	@timeout /t 10 /nobreak > nul 2>&1 || sleep 10
	@echo "🔍 Running comprehensive validation..."
	@python tests/runners/smart_validation.py --mode startup --verbose || (echo "⚠️  Validation failed but services are running" && true)
	@echo ""
	@echo "✅ Development environment ready!"
	@echo "📊 Access URLs:"
	@echo "  Frontend: http://localhost:3000"
	@echo "  API Gateway: http://localhost:8080"
	@echo "  Core API: http://localhost:8000"
	@echo "  API Docs: http://localhost:8000/docs"
	@echo "  Health Check: http://localhost:8000/health"
	@echo "  Grafana: http://localhost:3001"

# Interactive development mode
dev-interactive:
	@echo "🚀 Starting development environment in interactive mode..."
	@docker-compose up

down:
	@echo "🛑 Stopping services..."
	@docker-compose down

build:
	@echo "🔨 Building Docker images..."
	@docker-compose build

logs:
	docker-compose logs

logs-follow:
	docker-compose logs -f

restart:
	docker-compose restart

# Production commands
up-prod:
	docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

down-prod:
	docker-compose -f docker-compose.yml -f docker-compose.prod.yml down

build-prod:
	docker-compose -f docker-compose.yml -f docker-compose.prod.yml build

# Database commands
db-migrate:
	docker-compose exec core-api python -m alembic upgrade head

db-seed:
	docker-compose exec core-api python scripts/seed_data.py

db-backup:
	@mkdir -p ./database/backups
	docker-compose exec postgres pg_dump -U postgres arroyo_university > ./database/backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "Database backup created in ./database/backups/"

db-restore:
	@read -p "Enter backup file name (without path): " backup_file; \
	docker-compose exec -T postgres psql -U postgres -d arroyo_university < ./database/backups/$$backup_file

db-reset:
	@echo "WARNING: This will destroy all data!"
	@read -p "Are you sure? (y/N): " confirm; \
	if [ "$$confirm" = "y" ] || [ "$$confirm" = "Y" ]; then \
		docker-compose down; \
		docker volume rm arroyouniversity_postgres_data; \
		docker-compose up -d postgres; \
		sleep 10; \
		make db-migrate; \
	fi

# Testing commands
test:
	@echo "🧪 Running all tests..."
	make test-core
	make test-ai
	make test-frontend

test-core:
	docker-compose exec core-api python -m pytest tests/ -v

test-ai:
	docker-compose exec ai-service python -m pytest tests/ -v

test-frontend:
	docker-compose exec frontend npm test

test-coverage:
	docker-compose exec core-api python -m pytest tests/ --cov=app --cov-report=html
	docker-compose exec ai-service python -m pytest tests/ --cov=app --cov-report=html

# Sprint 1 Testing & Validation
validate:
	@echo "🔍 Running quick environment validation..."
	@python tests/runners/smart_validation.py --mode quick

validate-full:
	@echo "🔍 Running comprehensive validation..."
	@python tests/runners/smart_validation.py --mode full --verbose

validate-auto:
	@echo "⚡ Quick validation..."
	@python tests/runners/smart_validation.py --mode quick || echo "⚠️  Validation failed but continuing..."

test-sprint1:
	@echo "🧪 Running Sprint 1 test suite..."
	@python tests/runners/run_sprint1_tests.py

test-sprint1-verbose:
	@echo "🧪 Running Sprint 1 test suite (verbose)..."
	@python tests/runners/run_sprint1_tests.py --verbose

test-quick:
	@echo "⚡ Running quick acceptance tests..."
	@python -m pytest tests/unit/sprint1/test_infrastructure.py::TestAcceptanceCriteria -v

test-infrastructure:
	@echo "🏗️  Testing infrastructure..."
	@python -m pytest tests/unit/sprint1/test_infrastructure.py -v

test-multi-tenant:
	@echo "🏢 Testing multi-tenant architecture..."
	@python -m pytest tests/unit/sprint1/test_multi_tenant.py -v

test-database:
	@echo "🔒 Testing database security..."
	@python -m pytest tests/unit/sprint1/test_database_rls.py -v

test-integration:
	@echo "🔗 Testing integration..."
	@python -m pytest tests/integration/test_sprint1_integration.py -v

# Code quality commands
lint:
	@echo "Running linting..."
	docker-compose exec core-api python -m flake8 app/
	docker-compose exec core-api python -m mypy app/
	docker-compose exec ai-service python -m flake8 app/
	docker-compose exec ai-service python -m mypy app/
	docker-compose exec notification-service python -m flake8 app/
	docker-compose exec frontend npm run lint

format:
	@echo "Formatting code..."
	docker-compose exec core-api python -m black app/
	docker-compose exec core-api python -m isort app/
	docker-compose exec ai-service python -m black app/
	docker-compose exec ai-service python -m isort app/
	docker-compose exec notification-service python -m black app/
	docker-compose exec notification-service python -m isort app/
	docker-compose exec frontend npm run format

security-scan:
	@echo "Running security scans..."
	docker-compose exec core-api python -m bandit -r app/
	docker-compose exec ai-service python -m bandit -r app/
	docker-compose exec notification-service python -m bandit -r app/

# Utility commands
clean:
	@echo "🧹 Cleaning up Docker resources..."
	docker-compose down -v
	docker system prune -f
	docker volume prune -f

install-deps:
	@echo "📦 Installing development dependencies..."
	cd core-api && pip install -r requirements.txt
	cd ai-service && pip install -r requirements.txt
	cd notification-service && pip install -r requirements.txt
	cd frontend && npm install

status:
	@echo "📊 System Status:"
	@docker-compose ps
	@echo ""
	@echo "🔍 Quick Health Check:"
	@$(MAKE) debug-health

# Enhanced debugging commands
debug-health:
	@echo "🏥 Checking service health..."
	@echo "Backend Health:"
	@curl -s http://localhost:8000/health | jq . 2>/dev/null || curl -s http://localhost:8000/health || echo "❌ Backend not responding"
	@echo ""
	@echo "Database Health:"
	@docker-compose exec -T postgres pg_isready -U postgres 2>/dev/null || echo "❌ Database not ready"
	@echo ""
	@echo "Redis Health:"
	@docker-compose exec -T redis redis-cli ping 2>/dev/null || echo "❌ Redis not responding"

# Development workflow shortcuts
fresh-start:
	@echo "🆕 Fresh start - cleaning and rebuilding everything..."
	@$(MAKE) clean
	@$(MAKE) dev-setup
	@$(MAKE) dev-with-tests

quick-restart:
	@echo "⚡ Quick restart..."
	@docker-compose restart
	@sleep 5
	@$(MAKE) validate

rebuild:
	@echo "🔄 Rebuilding and restarting..."
	@$(MAKE) clean
	@$(MAKE) build
	@$(MAKE) dev-with-tests

shell-core:
	docker-compose exec core-api bash

shell-ai:
	docker-compose exec ai-service bash

shell-notification:
	docker-compose exec notification-service bash

shell-db:
	docker-compose exec postgres psql -U postgres -d arroyo_university

# Monitoring commands
monitoring-up:
	docker-compose up -d prometheus grafana loki

monitoring-down:
	docker-compose stop prometheus grafana loki

# Development helpers
dev-setup:
	@echo "Running automated development setup..."
	@chmod +x scripts/setup-dev.sh
	@./scripts/setup-dev.sh

dev-setup-manual: build up db-migrate
	@echo "Development environment setup complete!"
	@echo "Run 'make logs-follow' to see service logs"

# Quick commands for common operations
quick-restart-api:
	docker-compose restart core-api

quick-restart-ai:
	docker-compose restart ai-service

quick-restart-frontend:
	docker-compose restart frontend

# Health checks
health-check:
	@echo "Checking service health..."
	@curl -f http://localhost:8000/health || echo "Core API: DOWN"
	@curl -f http://localhost:8001/health || echo "AI Service: DOWN"
	@curl -f http://localhost:8002/health || echo "Notification Service: DOWN"
	@curl -f http://localhost:3000 || echo "Frontend: DOWN"

# Database operations
backup:
	@echo "📦 Creating database backup..."
	docker-compose exec postgres pg_dump -U postgres arroyo_university > backup_$(shell date +%Y%m%d_%H%M%S).sql

restore:
	@echo "📥 Restoring database from backup..."
	@read -p "Enter backup file name: " backup_file; \
	docker-compose exec -T postgres psql -U postgres arroyo_university < $$backup_file

# CI/CD helpers
ci-test:
	@echo "🤖 Running CI tests..."
	@python tests/runners/run_sprint1_tests.py --verbose

ci-validate:
	@echo "🤖 Running CI validation..."
	@python tests/runners/smart_validation.py --mode ci --verbose

# Configuration helpers
show-config:
	@echo "Current Configuration:"
	@echo "  VALIDATE_ON_START: $(VALIDATE_ON_START)"
	@echo "  QUICK_VALIDATE: $(QUICK_VALIDATE)"
	@echo "  VERBOSE_TESTS: $(VERBOSE_TESTS)"
	@echo "  SILENT_MODE: $(SILENT_MODE)"
	@echo "  CONTINUE_ON_ERROR: $(CONTINUE_ON_ERROR)"

# Load configuration presets
config-dev:
	@echo "🔧 Setting development configuration..."
	@$(eval VALIDATE_ON_START=false)
	@$(eval QUICK_VALIDATE=true)
	@$(eval VERBOSE_TESTS=false)
	@$(eval CONTINUE_ON_ERROR=true)
	@$(MAKE) show-config

config-startup:
	@echo "🚀 Setting startup configuration..."
	@$(eval VALIDATE_ON_START=true)
	@$(eval QUICK_VALIDATE=true)
	@$(eval VERBOSE_TESTS=false)
	@$(eval CONTINUE_ON_ERROR=true)
	@$(MAKE) show-config

config-ci:
	@echo "🤖 Setting CI configuration..."
	@$(eval VALIDATE_ON_START=true)
	@$(eval QUICK_VALIDATE=false)
	@$(eval VERBOSE_TESTS=true)
	@$(eval CONTINUE_ON_ERROR=false)
	@$(MAKE) show-config

# Smart validation using Python script
validate-smart:
	@echo "🧠 Running smart validation..."
	@python tests/runners/smart_validation.py --mode quick

validate-smart-full:
	@echo "🧠 Running comprehensive smart validation..."
	@python tests/runners/smart_validation.py --mode full --verbose

validate-smart-ci:
	@echo "🧠 Running CI smart validation..."
	@python tests/runners/smart_validation.py --mode ci --verbose

# Simplified mode commands (Windows compatible)
dev-mode:
	@echo "🔧 Development mode: Quick startup without validation"
	@$(MAKE) dev

startup-mode:
	@echo "🚀 Startup mode: Full startup with validation"
	@$(MAKE) dev-with-tests

ci-mode:
	@echo "🤖 CI mode: Comprehensive testing"
	@$(MAKE) ci-test
