"""
Mock backend modules for testing when actual backend is not available.
This allows tests to run without requiring the full backend implementation.
"""

import sys
from unittest.mock import <PERSON><PERSON><PERSON>, Mock
from sqlalchemy.ext.declarative import declarative_base
from fastapi import FastAPI

# Create mock base for SQLAlchemy models
MockBase = declarative_base()

class MockTenant:
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)

class MockUser:
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)

class MockSecurity:
    @staticmethod
    def get_password_hash(password: str) -> str:
        return f"hashed_{password}"
    
    @staticmethod
    def create_access_token(data: dict) -> str:
        return "mock_access_token"

class MockTenantContext:
    def __init__(self, db, tenant_id):
        self.db = db
        self.tenant_id = tenant_id
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        pass

# Create mock modules
backend_mock = MagicMock()
backend_mock.src = MagicMock()
backend_mock.src.models = MagicMock()
backend_mock.src.models.base = MagicMock()
backend_mock.src.models.base.Base = MockBase
backend_mock.src.models.tenant = MagicMock()
backend_mock.src.models.tenant.Tenant = MockTenant
backend_mock.src.models.user = MagicMock()
backend_mock.src.models.user.User = MockUser
backend_mock.src.core = MagicMock()
backend_mock.src.core.security = MockSecurity()
backend_mock.src.core.database = MagicMock()
backend_mock.src.core.database.get_db = MagicMock()
backend_mock.src.core.tenant_context = MagicMock()
backend_mock.src.core.tenant_context.tenant_context = MockTenantContext
backend_mock.src.main = MagicMock()
backend_mock.src.main.app = FastAPI()

def setup_backend_mocks():
    """Setup backend mocks in sys.modules"""
    sys.modules['backend'] = backend_mock
    sys.modules['backend.src'] = backend_mock.src
    sys.modules['backend.src.models'] = backend_mock.src.models
    sys.modules['backend.src.models.base'] = backend_mock.src.models.base
    sys.modules['backend.src.models.tenant'] = backend_mock.src.models.tenant
    sys.modules['backend.src.models.user'] = backend_mock.src.models.user
    sys.modules['backend.src.core'] = backend_mock.src.core
    sys.modules['backend.src.core.security'] = backend_mock.src.core.security
    sys.modules['backend.src.core.database'] = backend_mock.src.core.database
    sys.modules['backend.src.core.tenant_context'] = backend_mock.src.core.tenant_context
    sys.modules['backend.src.main'] = backend_mock.src.main

# Auto-setup when imported
setup_backend_mocks()
