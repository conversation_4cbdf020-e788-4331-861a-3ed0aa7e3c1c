"""
Sprint 1 Database RLS (Row Level Security) Tests
Tests for User Story #3: PostgreSQL Database with RLS

These tests validate that Row Level Security is properly implemented
and enforces tenant data isolation at the database level.
"""

import pytest
import uuid
from sqlalchemy import text
from backend.src.models.tenant import Tenant
from backend.src.models.user import User
from backend.src.core.tenant_context import tenant_context
from backend.src.core.security import get_password_hash


class TestRLSFunctions:
    """Test RLS support functions in PostgreSQL."""
    
    def test_current_tenant_id_function_exists(self, test_db):
        """Test that current_tenant_id() function is available."""
        result = test_db.execute(text("""
            SELECT EXISTS (
                SELECT FROM pg_proc 
                WHERE proname = 'current_tenant_id'
            );
        """)).fetchone()
        assert result[0] is True
    
    def test_set_tenant_context_function_exists(self, test_db):
        """Test that set_tenant_context() function is available."""
        result = test_db.execute(text("""
            SELECT EXISTS (
                SELECT FROM pg_proc 
                WHERE proname = 'set_tenant_context'
            );
        """)).fetchone()
        assert result[0] is True
    
    def test_tenant_isolation_policy_function_exists(self, test_db):
        """Test that tenant_isolation_policy() function is available."""
        result = test_db.execute(text("""
            SELECT EXISTS (
                SELECT FROM pg_proc 
                WHERE proname = 'tenant_isolation_policy'
            );
        """)).fetchone()
        assert result[0] is True
    
    def test_current_tenant_id_function_works(self, test_db, sample_tenant):
        """Test that current_tenant_id() function returns correct value."""
        # Set tenant context
        test_db.execute(text("SELECT set_config('app.current_tenant_id', :tenant_id, false)"), 
                       {"tenant_id": sample_tenant.id})
        
        # Get current tenant ID
        result = test_db.execute(text("SELECT current_tenant_id()")).scalar()
        assert result == sample_tenant.id
    
    def test_set_tenant_context_function_works(self, test_db, sample_tenant):
        """Test that set_tenant_context() function works correctly."""
        # Use the function to set context
        test_db.execute(text("SELECT set_tenant_context(:tenant_id)"), 
                       {"tenant_id": sample_tenant.id})
        
        # Verify context was set
        result = test_db.execute(text("SELECT current_tenant_id()")).scalar()
        assert result == sample_tenant.id


class TestRLSPolicies:
    """Test Row Level Security policies on tables."""
    
    def test_users_table_has_rls_enabled(self, test_db):
        """Test that RLS is enabled on users table."""
        result = test_db.execute(text("""
            SELECT relrowsecurity 
            FROM pg_class 
            WHERE relname = 'users';
        """)).fetchone()
        assert result[0] is True
    
    def test_users_table_has_rls_policies(self, test_db):
        """Test that RLS policies exist on users table."""
        result = test_db.execute(text("""
            SELECT COUNT(*) 
            FROM pg_policies 
            WHERE tablename = 'users';
        """)).fetchone()
        assert result[0] > 0  # Should have at least one policy
    
    def test_rls_policy_names_exist(self, test_db):
        """Test that expected RLS policy names exist."""
        expected_policies = ['tenant_isolation_policy']
        
        for policy_name in expected_policies:
            result = test_db.execute(text("""
                SELECT EXISTS (
                    SELECT FROM pg_policies 
                    WHERE tablename = 'users' 
                    AND policyname = :policy_name
                );
            """), {"policy_name": policy_name}).fetchone()
            assert result[0] is True, f"Policy {policy_name} should exist"
    
    def test_rls_policy_applies_to_all_operations(self, test_db):
        """Test that RLS policies apply to SELECT, INSERT, UPDATE, DELETE."""
        result = test_db.execute(text("""
            SELECT cmd 
            FROM pg_policies 
            WHERE tablename = 'users' 
            AND policyname = 'tenant_isolation_policy';
        """)).fetchone()
        
        # Policy should apply to ALL operations or specific ones
        assert result[0] in ['ALL', 'SELECT', 'INSERT', 'UPDATE', 'DELETE']


class TestRLSDataIsolation:
    """Test that RLS properly isolates tenant data."""
    
    def test_rls_isolates_user_data_by_tenant(self, test_db, sample_tenant, sample_tenant_2):
        """Test that RLS prevents cross-tenant data access."""
        # Create users for different tenants
        user1 = User(
            tenant_id=sample_tenant.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="user"
        )
        
        user2 = User(
            tenant_id=sample_tenant_2.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="user"
        )
        
        test_db.add_all([user1, user2])
        test_db.commit()
        
        # Set context to first tenant
        with tenant_context(test_db, sample_tenant.id):
            # Should only see users from first tenant
            users = test_db.query(User).all()
            tenant_ids = [u.tenant_id for u in users]
            
            # All returned users should belong to the current tenant
            for tenant_id in tenant_ids:
                assert tenant_id == sample_tenant.id
    
    def test_rls_prevents_unauthorized_inserts(self, test_db, sample_tenant, sample_tenant_2):
        """Test that RLS prevents inserting data for wrong tenant."""
        # Set context to first tenant
        with tenant_context(test_db, sample_tenant.id):
            # Try to insert user for different tenant
            user = User(
                tenant_id=sample_tenant_2.id,  # Different tenant!
                email="<EMAIL>",
                hashed_password=get_password_hash("password123"),
                role="user"
            )
            
            test_db.add(user)
            
            # This should either fail or the user should not be visible
            # depending on RLS policy implementation
            try:
                test_db.commit()
                test_db.refresh(user)
                
                # If insert succeeded, verify we can't see it in current context
                found_user = test_db.query(User).filter(
                    User.email == "<EMAIL>"
                ).first()
                
                # Should not find the user due to RLS
                assert found_user is None
                
            except Exception:
                # Insert failed due to RLS - this is also acceptable
                test_db.rollback()
    
    def test_rls_prevents_unauthorized_updates(self, test_db, sample_tenant, sample_tenant_2):
        """Test that RLS prevents updating data from other tenants."""
        # Create user in second tenant
        user = User(
            tenant_id=sample_tenant_2.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="user"
        )
        test_db.add(user)
        test_db.commit()
        user_id = user.id
        
        # Set context to first tenant
        with tenant_context(test_db, sample_tenant.id):
            # Try to update user from different tenant
            update_result = test_db.execute(text("""
                UPDATE users 
                SET first_name = 'Hacked' 
                WHERE id = :user_id
            """), {"user_id": user_id})
            
            test_db.commit()
            
            # Update should affect 0 rows due to RLS
            assert update_result.rowcount == 0
    
    def test_rls_prevents_unauthorized_deletes(self, test_db, sample_tenant, sample_tenant_2):
        """Test that RLS prevents deleting data from other tenants."""
        # Create user in second tenant
        user = User(
            tenant_id=sample_tenant_2.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="user"
        )
        test_db.add(user)
        test_db.commit()
        user_id = user.id
        
        # Set context to first tenant
        with tenant_context(test_db, sample_tenant.id):
            # Try to delete user from different tenant
            delete_result = test_db.execute(text("""
                DELETE FROM users WHERE id = :user_id
            """), {"user_id": user_id})
            
            test_db.commit()
            
            # Delete should affect 0 rows due to RLS
            assert delete_result.rowcount == 0
        
        # Verify user still exists in correct tenant context
        with tenant_context(test_db, sample_tenant_2.id):
            existing_user = test_db.query(User).filter(User.id == user_id).first()
            assert existing_user is not None


class TestRLSPerformance:
    """Test that RLS doesn't significantly impact performance."""
    
    def test_rls_query_performance_with_index(self, test_db, sample_tenant):
        """Test that RLS queries use indexes efficiently."""
        # Create multiple users for performance testing
        users = []
        for i in range(100):
            user = User(
                tenant_id=sample_tenant.id,
                email=f"perf{i}@tenant.com",
                hashed_password=get_password_hash("password123"),
                role="user"
            )
            users.append(user)
        
        test_db.add_all(users)
        test_db.commit()
        
        # Test query performance with RLS
        with tenant_context(test_db, sample_tenant.id):
            # This query should use the tenant_id index
            result = test_db.execute(text("""
                EXPLAIN (ANALYZE, BUFFERS) 
                SELECT * FROM users WHERE email LIKE 'perf%'
            """))
            
            query_plan = result.fetchall()
            plan_text = '\n'.join([str(row[0]) for row in query_plan])
            
            # Should use index scan, not sequential scan for good performance
            # Note: This is a basic check - actual optimization may vary
            assert "Index" in plan_text or "Bitmap" in plan_text or len(users) < 50
    
    def test_rls_overhead_acceptable(self, test_db, sample_tenant):
        """Test that RLS doesn't add excessive overhead."""
        import time
        
        # Create test data
        users = []
        for i in range(50):
            user = User(
                tenant_id=sample_tenant.id,
                email=f"overhead{i}@tenant.com",
                hashed_password=get_password_hash("password123"),
                role="user"
            )
            users.append(user)
        
        test_db.add_all(users)
        test_db.commit()
        
        # Measure query time with RLS
        with tenant_context(test_db, sample_tenant.id):
            start_time = time.time()
            
            for _ in range(10):  # Run multiple queries
                result = test_db.query(User).filter(
                    User.email.like("overhead%")
                ).all()
                assert len(result) > 0
            
            end_time = time.time()
            
            # Should complete reasonably quickly
            total_time = end_time - start_time
            assert total_time < 1.0, f"RLS queries took {total_time:.2f}s, should be under 1s"


class TestRLSEdgeCases:
    """Test RLS behavior in edge cases and error conditions."""
    
    def test_rls_with_no_tenant_context(self, test_db, sample_tenant):
        """Test RLS behavior when no tenant context is set."""
        # Create a user
        user = User(
            tenant_id=sample_tenant.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="user"
        )
        test_db.add(user)
        test_db.commit()
        
        # Clear any existing context
        test_db.execute(text("SELECT set_config('app.current_tenant_id', '', false)"))
        
        # Query without tenant context
        users = test_db.query(User).all()
        
        # Should return no users or handle gracefully
        # Behavior depends on RLS policy implementation
        assert isinstance(users, list)  # Should not crash
    
    def test_rls_with_invalid_tenant_context(self, test_db, sample_tenant):
        """Test RLS behavior with invalid tenant ID."""
        # Create a user
        user = User(
            tenant_id=sample_tenant.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="user"
        )
        test_db.add(user)
        test_db.commit()
        
        # Set invalid tenant context
        invalid_tenant_id = str(uuid.uuid4())
        test_db.execute(text("SELECT set_config('app.current_tenant_id', :tenant_id, false)"), 
                       {"tenant_id": invalid_tenant_id})
        
        # Query with invalid context
        users = test_db.query(User).all()
        
        # Should return no users for invalid tenant
        assert len(users) == 0
    
    def test_rls_with_sql_injection_attempt(self, test_db, sample_tenant):
        """Test that RLS protects against SQL injection in tenant context."""
        # Create test user
        user = User(
            tenant_id=sample_tenant.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="user"
        )
        test_db.add(user)
        test_db.commit()
        
        # Attempt SQL injection in tenant context
        malicious_input = f"{sample_tenant.id}' OR '1'='1"
        
        try:
            test_db.execute(text("SELECT set_config('app.current_tenant_id', :tenant_id, false)"), 
                           {"tenant_id": malicious_input})
            
            # Query should still be safe
            users = test_db.query(User).all()
            
            # Should not return unexpected results
            for user in users:
                # All users should have legitimate tenant IDs
                assert user.tenant_id != malicious_input
                
        except Exception:
            # If injection attempt fails, that's also acceptable
            pass


class TestAcceptanceCriteria:
    """Test Sprint 1 User Story #3 Acceptance Criteria."""
    
    def test_customer_data_isolated_at_database_level(self, test_db, sample_tenant, sample_tenant_2):
        """
        Acceptance Criteria: Customer data is isolated at the database level
        """
        # Create data for both tenants
        user1 = User(
            tenant_id=sample_tenant.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("secret123"),
            role="user"
        )
        
        user2 = User(
            tenant_id=sample_tenant_2.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("secret456"),
            role="user"
        )
        
        test_db.add_all([user1, user2])
        test_db.commit()
        
        # Test isolation at database level
        with tenant_context(test_db, sample_tenant.id):
            # Direct SQL query should only return tenant 1 data
            result = test_db.execute(text("""
                SELECT email FROM users WHERE email LIKE '%db_isolated%'
            """)).fetchall()
            
            emails = [row[0] for row in result]
            assert "<EMAIL>" in emails
            # Should not see data from other tenant due to RLS
    
    def test_security_works_automatically(self, test_db, sample_tenant):
        """
        Acceptance Criteria: Security works automatically without application code changes
        """
        # Create user
        user = User(
            tenant_id=sample_tenant.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="user"
        )
        test_db.add(user)
        test_db.commit()
        
        # Set tenant context
        with tenant_context(test_db, sample_tenant.id):
            # Raw SQL queries should automatically respect RLS
            result = test_db.execute(text("SELECT COUNT(*) FROM users")).scalar()
            assert result >= 1
            
            # ORM queries should also respect RLS automatically
            orm_result = test_db.query(User).count()
            assert orm_result >= 1
            
            # Both should return same count (RLS working automatically)
            assert result == orm_result
    
    def test_performance_remains_optimal(self, test_db, sample_tenant):
        """
        Acceptance Criteria: Performance remains optimal with security enabled
        """
        import time
        
        # Create test data
        users = []
        for i in range(100):
            user = User(
                tenant_id=sample_tenant.id,
                email=f"perf_test{i}@tenant.com",
                hashed_password=get_password_hash("password123"),
                role="user"
            )
            users.append(user)
        
        test_db.add_all(users)
        test_db.commit()
        
        # Test query performance
        with tenant_context(test_db, sample_tenant.id):
            start_time = time.time()
            
            # Perform multiple queries
            for _ in range(20):
                result = test_db.query(User).filter(
                    User.email.like("perf_test%")
                ).limit(10).all()
                assert len(result) == 10
            
            end_time = time.time()
            
            # Performance should be acceptable
            total_time = end_time - start_time
            assert total_time < 2.0, f"Performance test took {total_time:.2f}s, should be under 2s"
    
    def test_security_policies_auditable(self, test_db):
        """
        Acceptance Criteria: Security policies can be audited and verified
        """
        # Verify RLS policies are documented in system catalogs
        policies = test_db.execute(text("""
            SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
            FROM pg_policies 
            WHERE tablename = 'users'
            ORDER BY policyname;
        """)).fetchall()
        
        assert len(policies) > 0, "Should have auditable RLS policies"
        
        # Verify policy details are accessible for audit
        for policy in policies:
            assert policy[0] is not None  # schemaname
            assert policy[1] == 'users'   # tablename
            assert policy[2] is not None  # policyname
            # Other fields may be None depending on policy configuration
    
    def test_system_passes_security_testing(self, test_db, sample_tenant, sample_tenant_2):
        """
        Acceptance Criteria: System passes security penetration testing
        """
        # Simulate penetration testing scenarios
        
        # Test 1: Attempt to access other tenant's data
        user1 = User(
            tenant_id=sample_tenant.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="user"
        )
        
        user2 = User(
            tenant_id=sample_tenant_2.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="user"
        )
        
        test_db.add_all([user1, user2])
        test_db.commit()
        
        # Test 2: Context switching attack
        with tenant_context(test_db, sample_tenant.id):
            # Should only see tenant 1 data
            visible_users = test_db.query(User).filter(
                User.email.like("pentest%")
            ).all()
            
            for user in visible_users:
                assert user.tenant_id == sample_tenant.id
        
        # Test 3: Direct SQL bypass attempt
        with tenant_context(test_db, sample_tenant.id):
            # Even direct SQL should respect RLS
            result = test_db.execute(text("""
                SELECT tenant_id FROM users WHERE email LIKE 'pentest%'
            """)).fetchall()
            
            for row in result:
                assert row[0] == sample_tenant.id
        
        # If we reach here without seeing cross-tenant data, security test passes
        assert True, "Security penetration test passed"
