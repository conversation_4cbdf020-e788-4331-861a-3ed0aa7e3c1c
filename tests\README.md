# ArroyoUniversity Test Suite

## Overview
This directory contains comprehensive test cases for validating Sprint implementations, organized by sprint and functionality.

## Test Structure

```
tests/
├── README.md                          # This file
├── sprint-1/                          # Sprint 1 specific tests
│   ├── test_infrastructure.py         # Infrastructure and environment tests
│   ├── test_multi_tenant.py          # Multi-tenant architecture tests
│   ├── test_database_rls.py          # Database RLS security tests
│   └── conftest.py                    # Sprint 1 test fixtures
├── sprint-2/                          # Sprint 2 specific tests (future)
├── sprint-3/                          # Sprint 3 specific tests (future)
├── integration/                       # Cross-sprint integration tests
├── performance/                       # Performance and load tests
├── security/                          # Security-specific tests
└── fixtures/                          # Shared test data and fixtures
```

## Running Tests

### Prerequisites
```bash
# Ensure development environment is running
make dev

# Install test dependencies
pip install pytest pytest-asyncio pytest-cov httpx
```

### Run All Sprint 1 Tests
```bash
# From project root
pytest tests/sprint-1/ -v

# With coverage
pytest tests/sprint-1/ -v --cov=backend/src --cov-report=html
```

### Run Specific Test Categories
```bash
# Infrastructure tests only
pytest tests/sprint-1/test_infrastructure.py -v

# Multi-tenant tests only
pytest tests/sprint-1/test_multi_tenant.py -v

# Database RLS tests only
pytest tests/sprint-1/test_database_rls.py -v
```

### Run Integration Tests
```bash
pytest tests/integration/ -v
```

## Test Categories

### 🏗️ Infrastructure Tests
- Docker environment validation
- Service connectivity
- Environment configuration
- Health checks

### 🏢 Multi-tenant Tests
- Tenant creation and management
- Data isolation validation
- Context switching
- Configuration management

### 🔒 Database RLS Tests
- Row Level Security policies
- Tenant data isolation
- Security policy enforcement
- Performance with RLS enabled

### 🔗 Integration Tests
- End-to-end workflows
- Cross-service communication
- API endpoint validation
- Error handling scenarios

## Test Data Management

### Fixtures
- Test fixtures are defined in `conftest.py` files
- Shared fixtures are in `fixtures/` directory
- Each sprint has its own fixture set

### Test Database
- Tests use a separate test database
- Database is reset between test runs
- Test data is automatically cleaned up

## Continuous Integration

These tests are designed to run in CI/CD pipelines:
- GitHub Actions integration
- Docker-based test execution
- Coverage reporting
- Performance benchmarking

## Writing New Tests

### Test Naming Convention
- `test_<functionality>_<scenario>.py` for test files
- `test_<action>_<expected_result>` for test functions

### Test Structure
```python
def test_feature_scenario():
    """Test description explaining what is being tested"""
    # Arrange - Set up test data
    # Act - Execute the functionality
    # Assert - Verify the results
```

### Best Practices
- Use descriptive test names
- Include docstrings explaining test purpose
- Use appropriate fixtures for setup
- Clean up test data after tests
- Test both success and failure scenarios
