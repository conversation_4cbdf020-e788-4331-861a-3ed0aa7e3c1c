"""
Sample Test Data for Sprint 1 Tests
Provides reusable test data and factory functions for creating test objects.
"""

import uuid
from datetime import datetime, timedelta
from backend.src.models.tenant import Tenant
from backend.src.models.user import User
from backend.src.core.security import get_password_hash


class TenantFactory:
    """Factory for creating test tenants."""
    
    @staticmethod
    def create_tenant(
        name="Test University",
        slug=None,
        domain=None,
        is_active=True,
        settings=None,
        **kwargs
    ):
        """Create a test tenant with default or custom values."""
        if slug is None:
            slug = name.lower().replace(" ", "-")
        
        if domain is None:
            domain = f"{slug}.example.com"
        
        if settings is None:
            settings = {
                "theme": "blue",
                "max_users": 100,
                "features": {
                    "ai_enabled": True,
                    "analytics": True
                }
            }
        
        return Tenant(
            name=name,
            slug=slug,
            domain=domain,
            is_active=is_active,
            settings=settings,
            primary_color="#007bff",
            secondary_color="#6c757d",
            contact_email=f"admin@{domain}",
            **kwargs
        )
    
    @staticmethod
    def create_multiple_tenants(count=3):
        """Create multiple test tenants."""
        tenants = []
        for i in range(count):
            tenant = TenantFactory.create_tenant(
                name=f"University {i+1}",
                slug=f"university-{i+1}",
                domain=f"university{i+1}.example.com"
            )
            tenants.append(tenant)
        return tenants


class UserFactory:
    """Factory for creating test users."""
    
    @staticmethod
    def create_user(
        tenant_id,
        email=None,
        username=None,
        password="testpassword123",
        role="user",
        is_active=True,
        is_verified=True,
        **kwargs
    ):
        """Create a test user with default or custom values."""
        if email is None:
            email = f"user{uuid.uuid4().hex[:8]}@test.com"
        
        if username is None:
            username = email.split("@")[0]
        
        return User(
            tenant_id=tenant_id,
            email=email,
            username=username,
            hashed_password=get_password_hash(password),
            role=role,
            is_active=is_active,
            is_verified=is_verified,
            first_name="Test",
            last_name="User",
            **kwargs
        )
    
    @staticmethod
    def create_admin_user(tenant_id, email=None, **kwargs):
        """Create a test admin user."""
        if email is None:
            email = f"admin{uuid.uuid4().hex[:8]}@test.com"
        
        return UserFactory.create_user(
            tenant_id=tenant_id,
            email=email,
            role="admin",
            first_name="Admin",
            last_name="User",
            **kwargs
        )
    
    @staticmethod
    def create_owner_user(tenant_id, email=None, **kwargs):
        """Create a test owner user."""
        if email is None:
            email = f"owner{uuid.uuid4().hex[:8]}@test.com"
        
        return UserFactory.create_user(
            tenant_id=tenant_id,
            email=email,
            role="owner",
            first_name="Owner",
            last_name="User",
            **kwargs
        )
    
    @staticmethod
    def create_multiple_users(tenant_id, count=5, role="user"):
        """Create multiple test users for a tenant."""
        users = []
        for i in range(count):
            user = UserFactory.create_user(
                tenant_id=tenant_id,
                email=f"user{i+1}@test.com",
                username=f"user{i+1}",
                role=role,
                first_name=f"User{i+1}",
                last_name="Test"
            )
            users.append(user)
        return users


class TestScenarios:
    """Pre-defined test scenarios for common testing patterns."""
    
    @staticmethod
    def multi_tenant_isolation_scenario():
        """Create scenario for testing multi-tenant isolation."""
        # Create two tenants
        tenant1 = TenantFactory.create_tenant(
            name="Secure University 1",
            slug="secure-1",
            domain="secure1.example.com"
        )
        
        tenant2 = TenantFactory.create_tenant(
            name="Secure University 2", 
            slug="secure-2",
            domain="secure2.example.com"
        )
        
        return {
            "tenant1": tenant1,
            "tenant2": tenant2,
            "users1": [],  # To be populated after tenant1 is saved
            "users2": []   # To be populated after tenant2 is saved
        }
    
    @staticmethod
    def role_hierarchy_scenario(tenant_id):
        """Create scenario for testing role hierarchy."""
        owner = UserFactory.create_owner_user(
            tenant_id=tenant_id,
            email="<EMAIL>"
        )
        
        admin = UserFactory.create_admin_user(
            tenant_id=tenant_id,
            email="<EMAIL>"
        )
        
        regular_users = UserFactory.create_multiple_users(
            tenant_id=tenant_id,
            count=3,
            role="user"
        )
        
        return {
            "owner": owner,
            "admin": admin,
            "users": regular_users
        }
    
    @staticmethod
    def performance_test_scenario(tenant_id, user_count=100):
        """Create scenario for performance testing."""
        users = []
        for i in range(user_count):
            user = UserFactory.create_user(
                tenant_id=tenant_id,
                email=f"perf_user_{i}@test.com",
                username=f"perf_user_{i}",
                first_name=f"Performance",
                last_name=f"User{i}"
            )
            users.append(user)
        
        return {
            "users": users,
            "user_count": user_count
        }


class TestDataSets:
    """Pre-defined data sets for specific test types."""
    
    VALID_TENANT_DATA = [
        {
            "name": "Harvard University",
            "slug": "harvard",
            "domain": "harvard.edu",
            "settings": {"theme": "crimson", "max_users": 10000}
        },
        {
            "name": "MIT",
            "slug": "mit", 
            "domain": "mit.edu",
            "settings": {"theme": "tech", "max_users": 5000}
        },
        {
            "name": "Stanford University",
            "slug": "stanford",
            "domain": "stanford.edu", 
            "settings": {"theme": "cardinal", "max_users": 8000}
        }
    ]
    
    INVALID_TENANT_DATA = [
        {
            "name": "",  # Empty name
            "slug": "invalid-empty-name",
            "domain": "invalid1.com"
        },
        {
            "name": "Invalid Slug University",
            "slug": "invalid slug with spaces",  # Invalid slug
            "domain": "invalid2.com"
        },
        {
            "name": "Invalid Domain University",
            "slug": "invalid-domain",
            "domain": "not-a-valid-domain"  # Invalid domain
        }
    ]
    
    VALID_USER_DATA = [
        {
            "email": "<EMAIL>",
            "username": "johndoe",
            "first_name": "John",
            "last_name": "Doe",
            "role": "user"
        },
        {
            "email": "<EMAIL>",
            "username": "janeadmin",
            "first_name": "Jane",
            "last_name": "Admin",
            "role": "admin"
        },
        {
            "email": "<EMAIL>",
            "username": "systemowner",
            "first_name": "System",
            "last_name": "Owner",
            "role": "owner"
        }
    ]
    
    INVALID_USER_DATA = [
        {
            "email": "invalid-email",  # Invalid email format
            "username": "invaliduser1",
            "role": "user"
        },
        {
            "email": "<EMAIL>",
            "username": "",  # Empty username
            "role": "user"
        },
        {
            "email": "<EMAIL>",
            "username": "invalidrole",
            "role": "invalid_role"  # Invalid role
        }
    ]


class DatabaseTestHelpers:
    """Helper functions for database testing."""
    
    @staticmethod
    def create_test_tenant_with_users(db_session, user_count=5):
        """Create a tenant with specified number of users."""
        tenant = TenantFactory.create_tenant()
        db_session.add(tenant)
        db_session.commit()
        db_session.refresh(tenant)
        
        users = UserFactory.create_multiple_users(tenant.id, user_count)
        db_session.add_all(users)
        db_session.commit()
        
        for user in users:
            db_session.refresh(user)
        
        return tenant, users
    
    @staticmethod
    def create_multi_tenant_test_data(db_session, tenant_count=3, users_per_tenant=5):
        """Create multiple tenants with users for isolation testing."""
        test_data = []
        
        for i in range(tenant_count):
            tenant, users = DatabaseTestHelpers.create_test_tenant_with_users(
                db_session, users_per_tenant
            )
            test_data.append({
                "tenant": tenant,
                "users": users
            })
        
        return test_data
    
    @staticmethod
    def cleanup_test_data(db_session, tenants=None, users=None):
        """Clean up test data from database."""
        if users:
            for user in users:
                db_session.delete(user)
        
        if tenants:
            for tenant in tenants:
                db_session.delete(tenant)
        
        db_session.commit()


# Export commonly used items
__all__ = [
    'TenantFactory',
    'UserFactory', 
    'TestScenarios',
    'TestDataSets',
    'DatabaseTestHelpers'
]
