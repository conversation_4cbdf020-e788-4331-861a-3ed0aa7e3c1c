

services:
  # Database
  postgres:
    image: postgres:15-alpine
    container_name: arroyo-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-arroyo_university}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres123}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - arroyo-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and message queue
  redis:
    image: redis:7-alpine
    container_name: arroyo-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis123}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - arroyo-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Object Storage (MinIO for local development)
  minio:
    image: minio/minio:latest
    container_name: arroyo-minio
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-minioadmin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-minioadmin123}
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    networks:
      - arroyo-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API Gateway
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    container_name: arroyo-api-gateway
    env_file:
      - ./api-gateway/.env
    ports:
      - "8080:80"
      - "8443:443"
    depends_on:
      - core-api
      - notification-service
    networks:
      - arroyo-network
    volumes:
      - ./api-gateway/nginx.conf:/etc/nginx/nginx.conf
      - ./api-gateway/ssl:/etc/nginx/ssl

  # Core API Service
  core-api:
    build:
      context: ./core-api
      dockerfile: Dockerfile
    container_name: arroyo-core-api
    env_file:
      - ./core-api/.env
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres123}@postgres:5432/${POSTGRES_DB:-arroyo_university}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis123}@redis:6379/0
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - AZURE_SPEECH_KEY=${AZURE_SPEECH_KEY}
      - AZURE_SPEECH_REGION=${AZURE_SPEECH_REGION}
      - TURNITIN_API_KEY=${TURNITIN_API_KEY}
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - arroyo-network
    volumes:
      - ./core-api:/app
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # AI Services are now integrated into core-api
  # This saves infrastructure costs and simplifies deployment

  # Notification Service
  notification-service:
    build:
      context: ./notification-service
      dockerfile: Dockerfile
    container_name: arroyo-notification-service
    env_file:
      - ./notification-service/.env
    environment:
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis123}@redis:6379/2
      - DATABASE_URL=postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres123}@postgres:5432/${POSTGRES_DB:-arroyo_university}
    ports:
      - "8002:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - arroyo-network
    volumes:
      - ./notification-service:/app

  # Frontend SPA
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    container_name: arroyo-frontend
    env_file:
      - ./frontend/.env
    ports:
      - "3000:3000"
    networks:
      - arroyo-network
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - api-gateway

  # Monitoring Services
  prometheus:
    image: prom/prometheus:latest
    container_name: arroyo-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus:/etc/prometheus
      - prometheus_data:/prometheus
    networks:
      - arroyo-network

  grafana:
    image: grafana/grafana:latest
    container_name: arroyo-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    networks:
      - arroyo-network
    depends_on:
      - prometheus

  loki:
    image: grafana/loki:latest
    container_name: arroyo-loki
    command: -config.file=/etc/loki/local-config.yaml
    ports:
      - "3100:3100"
    volumes:
      - ./monitoring/loki:/etc/loki
      - loki_data:/loki
    networks:
      - arroyo-network

volumes:
  postgres_data:
  redis_data:
  minio_data:
  prometheus_data:
  grafana_data:
  loki_data:

networks:
  arroyo-network:
    driver: bridge
