"""
Sprint 1 Multi-tenant Architecture Tests
Tests for User Story #2: Basic Multi-tenant Architecture

These tests validate that the multi-tenant architecture properly isolates
tenant data and supports multiple organizations on the same platform.
"""

import pytest
import uuid
from sqlalchemy import text
from backend.src.models.tenant import Tenant
from backend.src.models.user import User
from backend.src.core.tenant_context import tenant_context
from backend.src.core.security import get_password_hash


class TestTenantModel:
    """Test tenant data model functionality."""
    
    def test_tenant_creation(self, test_db):
        """Test that tenants can be created with all required fields."""
        tenant = Tenant(
            name="Test University",
            slug="test-university",
            domain="test.example.com",
            is_active=True,
            settings={"theme": "blue", "max_users": 100},
            primary_color="#007bff",
            secondary_color="#6c757d",
            contact_email="<EMAIL>"
        )
        
        test_db.add(tenant)
        test_db.commit()
        test_db.refresh(tenant)
        
        assert tenant.id is not None
        assert tenant.name == "Test University"
        assert tenant.slug == "test-university"
        assert tenant.is_active is True
        assert tenant.settings["theme"] == "blue"
        assert tenant.primary_color == "#007bff"
    
    def test_tenant_unique_constraints(self, test_db):
        """Test that tenant slug and domain must be unique."""
        # Create first tenant
        tenant1 = Tenant(
            name="First University",
            slug="unique-slug",
            domain="unique.example.com"
        )
        test_db.add(tenant1)
        test_db.commit()
        
        # Try to create second tenant with same slug
        tenant2 = Tenant(
            name="Second University",
            slug="unique-slug",  # Same slug
            domain="different.example.com"
        )
        test_db.add(tenant2)
        
        with pytest.raises(Exception):  # Should raise integrity error
            test_db.commit()
        
        test_db.rollback()
        
        # Try to create tenant with same domain
        tenant3 = Tenant(
            name="Third University",
            slug="different-slug",
            domain="unique.example.com"  # Same domain
        )
        test_db.add(tenant3)
        
        with pytest.raises(Exception):  # Should raise integrity error
            test_db.commit()
    
    def test_tenant_soft_delete(self, test_db):
        """Test tenant soft delete functionality."""
        tenant = Tenant(
            name="Delete Test University",
            slug="delete-test"
        )
        test_db.add(tenant)
        test_db.commit()
        test_db.refresh(tenant)
        
        # Soft delete the tenant
        tenant.soft_delete()
        test_db.commit()
        
        assert tenant.is_deleted is True
        assert tenant.deleted_at is not None
    
    def test_tenant_configuration_support(self, test_db):
        """Test that tenants support custom configuration."""
        custom_settings = {
            "theme": "dark",
            "max_users": 500,
            "features": {
                "ai_enabled": True,
                "analytics": False
            },
            "branding": {
                "show_logo": True,
                "custom_css": "body { background: #000; }"
            }
        }
        
        tenant = Tenant(
            name="Custom Config University",
            slug="custom-config",
            settings=custom_settings,
            primary_color="#ff0000",
            secondary_color="#00ff00"
        )
        
        test_db.add(tenant)
        test_db.commit()
        test_db.refresh(tenant)
        
        assert tenant.settings["theme"] == "dark"
        assert tenant.settings["features"]["ai_enabled"] is True
        assert tenant.settings["branding"]["show_logo"] is True
        assert tenant.primary_color == "#ff0000"


class TestTenantDataIsolation:
    """Test that tenant data is properly isolated."""
    
    def test_users_belong_to_correct_tenant(self, test_db, sample_tenant, sample_tenant_2):
        """Test that users are associated with the correct tenant."""
        # Create user for first tenant
        user1 = User(
            tenant_id=sample_tenant.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="user"
        )
        
        # Create user for second tenant
        user2 = User(
            tenant_id=sample_tenant_2.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="user"
        )
        
        test_db.add_all([user1, user2])
        test_db.commit()
        
        # Verify users belong to correct tenants
        assert user1.tenant_id == sample_tenant.id
        assert user2.tenant_id == sample_tenant_2.id
        
        # Verify users have different tenant IDs
        assert user1.tenant_id != user2.tenant_id
    
    def test_tenant_context_isolation(self, test_db, sample_tenant, sample_tenant_2):
        """Test that tenant context properly isolates data access."""
        # Create users for different tenants
        user1 = User(
            tenant_id=sample_tenant.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="user"
        )
        
        user2 = User(
            tenant_id=sample_tenant_2.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="user"
        )
        
        test_db.add_all([user1, user2])
        test_db.commit()
        
        # Test access within first tenant context
        with tenant_context(test_db, sample_tenant.id):
            tenant1_users = test_db.query(User).all()
            tenant1_emails = [u.email for u in tenant1_users]
            
            assert "<EMAIL>" in tenant1_emails
            # Should not see users from other tenants due to RLS
            # Note: This test assumes RLS is properly configured
        
        # Test access within second tenant context
        with tenant_context(test_db, sample_tenant_2.id):
            tenant2_users = test_db.query(User).all()
            tenant2_emails = [u.email for u in tenant2_users]
            
            assert "<EMAIL>" in tenant2_emails
    
    def test_cross_tenant_data_access_prevention(self, test_db, sample_tenant, sample_tenant_2):
        """Test that users cannot access data from other tenants."""
        # Create test data for each tenant
        user1 = User(
            tenant_id=sample_tenant.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="user"
        )
        
        user2 = User(
            tenant_id=sample_tenant_2.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="user"
        )
        
        test_db.add_all([user1, user2])
        test_db.commit()
        
        # Try to access user2 while in tenant1 context
        with tenant_context(test_db, sample_tenant.id):
            # Should not be able to find user2 due to RLS
            found_user = test_db.query(User).filter(
                User.email == "<EMAIL>"
            ).first()
            
            # With proper RLS, this should be None
            # Note: This test validates RLS is working
            assert found_user is None or found_user.tenant_id == sample_tenant.id


class TestTenantContextManagement:
    """Test tenant context switching and management."""
    
    def test_tenant_context_setting(self, test_db, sample_tenant):
        """Test that tenant context can be set and retrieved."""
        with tenant_context(test_db, sample_tenant.id) as ctx:
            # Verify context is set
            result = test_db.execute(text("SELECT current_tenant_id()")).scalar()
            assert result == sample_tenant.id
            assert ctx.current_tenant_id == sample_tenant.id
    
    def test_tenant_context_clearing(self, test_db, sample_tenant):
        """Test that tenant context is properly cleared after use."""
        with tenant_context(test_db, sample_tenant.id):
            # Context should be set inside the block
            result = test_db.execute(text("SELECT current_tenant_id()")).scalar()
            assert result == sample_tenant.id
        
        # Context should be cleared outside the block
        result = test_db.execute(text("SELECT current_tenant_id()")).scalar()
        assert result == "" or result is None
    
    def test_nested_tenant_contexts(self, test_db, sample_tenant, sample_tenant_2):
        """Test that nested tenant contexts work correctly."""
        with tenant_context(test_db, sample_tenant.id):
            result1 = test_db.execute(text("SELECT current_tenant_id()")).scalar()
            assert result1 == sample_tenant.id
            
            with tenant_context(test_db, sample_tenant_2.id):
                result2 = test_db.execute(text("SELECT current_tenant_id()")).scalar()
                assert result2 == sample_tenant_2.id
            
            # Should return to first tenant context
            result3 = test_db.execute(text("SELECT current_tenant_id()")).scalar()
            assert result3 == sample_tenant.id
    
    def test_tenant_context_error_handling(self, test_db):
        """Test that tenant context handles errors gracefully."""
        invalid_tenant_id = str(uuid.uuid4())
        
        try:
            with tenant_context(test_db, invalid_tenant_id):
                # This should work even with invalid tenant ID
                # The context manager should handle cleanup
                result = test_db.execute(text("SELECT current_tenant_id()")).scalar()
                assert result == invalid_tenant_id
                
                # Simulate an error
                raise ValueError("Test error")
        except ValueError:
            pass  # Expected error
        
        # Context should be cleared even after error
        result = test_db.execute(text("SELECT current_tenant_id()")).scalar()
        assert result == "" or result is None


class TestTenantScalability:
    """Test that the tenant architecture can scale."""
    
    def test_multiple_tenants_creation(self, test_db):
        """Test that multiple tenants can be created and managed."""
        tenants = []
        
        for i in range(10):
            tenant = Tenant(
                name=f"University {i}",
                slug=f"university-{i}",
                domain=f"university{i}.example.com",
                settings={"theme": f"theme-{i}", "max_users": 100 + i}
            )
            tenants.append(tenant)
        
        test_db.add_all(tenants)
        test_db.commit()
        
        # Verify all tenants were created
        created_tenants = test_db.query(Tenant).filter(
            Tenant.name.like("University %")
        ).all()
        
        assert len(created_tenants) == 10
        
        # Verify each tenant has unique properties
        slugs = [t.slug for t in created_tenants]
        assert len(set(slugs)) == 10  # All slugs should be unique
    
    def test_tenant_user_relationships(self, test_db, sample_tenant):
        """Test that tenant-user relationships work correctly."""
        # Create multiple users for the same tenant
        users = []
        for i in range(5):
            user = User(
                tenant_id=sample_tenant.id,
                email=f"user{i}@{sample_tenant.slug}.com",
                hashed_password=get_password_hash("password123"),
                role="user"
            )
            users.append(user)
        
        test_db.add_all(users)
        test_db.commit()
        
        # Verify tenant has correct number of users
        test_db.refresh(sample_tenant)
        assert len(sample_tenant.users) == 5
        
        # Verify all users belong to the correct tenant
        for user in sample_tenant.users:
            assert user.tenant_id == sample_tenant.id


class TestAcceptanceCriteria:
    """Test Sprint 1 User Story #2 Acceptance Criteria."""
    
    def test_multiple_customers_simultaneous_use(self, test_db, sample_tenant, sample_tenant_2):
        """
        Acceptance Criteria: Multiple customers can use the platform simultaneously
        """
        # Create users for both tenants
        user1 = User(
            tenant_id=sample_tenant.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="user"
        )
        
        user2 = User(
            tenant_id=sample_tenant_2.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="user"
        )
        
        test_db.add_all([user1, user2])
        test_db.commit()
        
        # Both tenants should exist and be active
        assert sample_tenant.is_active is True
        assert sample_tenant_2.is_active is True
        
        # Both users should exist in their respective tenants
        assert user1.tenant_id == sample_tenant.id
        assert user2.tenant_id == sample_tenant_2.id
    
    def test_complete_data_isolation(self, test_db, sample_tenant, sample_tenant_2):
        """
        Acceptance Criteria: Each customer's data is completely isolated from others
        """
        # This test validates the core isolation requirement
        # Create data for each tenant and verify isolation
        
        user1 = User(
            tenant_id=sample_tenant.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="user"
        )
        
        user2 = User(
            tenant_id=sample_tenant_2.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="user"
        )
        
        test_db.add_all([user1, user2])
        test_db.commit()
        
        # Verify isolation through context switching
        with tenant_context(test_db, sample_tenant.id):
            tenant1_data = test_db.query(User).all()
            tenant1_emails = [u.email for u in tenant1_data]
            assert "<EMAIL>" in tenant1_emails
            # Should not see data from other tenant
        
        with tenant_context(test_db, sample_tenant_2.id):
            tenant2_data = test_db.query(User).all()
            tenant2_emails = [u.email for u in tenant2_data]
            assert "<EMAIL>" in tenant2_emails
    
    def test_custom_branding_and_settings(self, test_db):
        """
        Acceptance Criteria: Each customer can have custom branding and settings
        """
        tenant = Tenant(
            name="Branded University",
            slug="branded-university",
            settings={
                "theme": "custom",
                "features": {"ai_enabled": True},
                "notifications": {"email": True, "sms": False}
            },
            primary_color="#ff6b35",
            secondary_color="#004e89",
            logo_url="https://example.com/logo.png"
        )
        
        test_db.add(tenant)
        test_db.commit()
        test_db.refresh(tenant)
        
        # Verify custom branding is stored correctly
        assert tenant.primary_color == "#ff6b35"
        assert tenant.secondary_color == "#004e89"
        assert tenant.logo_url == "https://example.com/logo.png"
        assert tenant.settings["theme"] == "custom"
        assert tenant.settings["features"]["ai_enabled"] is True
    
    def test_unlimited_customer_scalability(self, test_db):
        """
        Acceptance Criteria: Platform can scale to unlimited number of customers
        """
        # Test creating many tenants to validate scalability
        batch_size = 50
        tenants = []
        
        for i in range(batch_size):
            tenant = Tenant(
                name=f"Scale Test University {i}",
                slug=f"scale-test-{i}",
                domain=f"scale{i}.example.com"
            )
            tenants.append(tenant)
        
        test_db.add_all(tenants)
        test_db.commit()
        
        # Verify all tenants were created successfully
        created_count = test_db.query(Tenant).filter(
            Tenant.name.like("Scale Test University %")
        ).count()
        
        assert created_count == batch_size
    
    def test_security_audit_compliance(self, test_db, sample_tenant, sample_tenant_2):
        """
        Acceptance Criteria: Security audit confirms no data leakage between customers
        """
        # Create sensitive data for each tenant
        sensitive_user1 = User(
            tenant_id=sample_tenant.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("topsecret123"),
            role="admin",
            first_name="Confidential",
            last_name="Data1"
        )
        
        sensitive_user2 = User(
            tenant_id=sample_tenant_2.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("topsecret456"),
            role="admin",
            first_name="Confidential",
            last_name="Data2"
        )
        
        test_db.add_all([sensitive_user1, sensitive_user2])
        test_db.commit()
        
        # Audit: Try to access cross-tenant data
        with tenant_context(test_db, sample_tenant.id):
            # Should not be able to find user from other tenant
            cross_tenant_access = test_db.query(User).filter(
                User.email == "<EMAIL>"
            ).first()
            
            # This should be None due to RLS (or only return same-tenant data)
            if cross_tenant_access is not None:
                assert cross_tenant_access.tenant_id == sample_tenant.id
        
        # Verify each tenant can only see their own data
        with tenant_context(test_db, sample_tenant.id):
            tenant1_users = test_db.query(User).filter(
                User.first_name == "Confidential"
            ).all()
            for user in tenant1_users:
                assert user.tenant_id == sample_tenant.id
        
        with tenant_context(test_db, sample_tenant_2.id):
            tenant2_users = test_db.query(User).filter(
                User.first_name == "Confidential"
            ).all()
            for user in tenant2_users:
                assert user.tenant_id == sample_tenant_2.id
