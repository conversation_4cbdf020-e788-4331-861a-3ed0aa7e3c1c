#!/usr/bin/env python3
"""
Test Folder Reorganization Script
Reorganizes the test folder structure to be cleaner and more organized.
"""

import os
import shutil
from pathlib import Path

def reorganize_tests():
    """Reorganize test folder structure."""
    
    # Define the new structure
    base_dir = Path("tests")
    
    # Create new directory structure
    new_dirs = [
        "unit/sprint1",
        "unit/sprint2", 
        "integration",
        "e2e",
        "performance",
        "security",
        "runners",
        "config",
        "utils",
        "fixtures",
        "docs"
    ]
    
    for dir_path in new_dirs:
        (base_dir / dir_path).mkdir(parents=True, exist_ok=True)
        # Create __init__.py files for Python packages
        if not dir_path.startswith("docs"):
            (base_dir / dir_path / "__init__.py").touch()
    
    # File mappings: old_path -> new_path
    file_mappings = {
        # Test files
        "sprint-1/test_infrastructure.py": "unit/sprint1/test_infrastructure.py",
        "sprint-1/test_multi_tenant.py": "unit/sprint1/test_multi_tenant.py", 
        "sprint-1/test_database_rls.py": "unit/sprint1/test_database_rls.py",
        "sprint-1/conftest.py": "unit/sprint1/conftest.py",
        
        # Integration tests
        "integration/test_sprint1_integration.py": "integration/test_sprint1_integration.py",
        
        # Runners
        "run_sprint1_tests.py": "runners/run_sprint1_tests.py",
        "smart_validation.py": "runners/smart_validation.py",
        
        # Configuration
        "test_config.env": "config/test_config.env",
        "pytest.ini": "config/pytest.ini",
        
        # Utilities
        "validate_sprint1.sh": "utils/validate_sprint1.sh",
        "demo_validation.sh": "utils/demo_validation.sh",
        
        # Fixtures
        "fixtures/sample_data.py": "fixtures/sample_data.py",
        
        # Documentation
        "README.md": "docs/README.md",
        "TESTING_GUIDE.md": "docs/TESTING_GUIDE.md",
    }
    
    # Move files
    for old_path, new_path in file_mappings.items():
        old_file = base_dir / old_path
        new_file = base_dir / new_path
        
        if old_file.exists():
            print(f"Moving {old_path} -> {new_path}")
            # Create parent directory if it doesn't exist
            new_file.parent.mkdir(parents=True, exist_ok=True)
            # Copy file (don't move to preserve originals for now)
            shutil.copy2(old_file, new_file)
        else:
            print(f"Warning: {old_path} not found")
    
    # Create new conftest.py at root level
    root_conftest = base_dir / "conftest.py"
    if not root_conftest.exists():
        with open(root_conftest, 'w') as f:
            f.write('''"""
Root conftest.py for ArroyoUniversity tests
Provides shared fixtures and configuration for all test modules.
"""

import pytest
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import shared fixtures
from tests.fixtures.sample_data import *
from tests.unit.sprint1.conftest import *

# Global test configuration
pytest_plugins = [
    "tests.fixtures.sample_data",
    "tests.unit.sprint1.conftest",
]
''')
    
    # Create main __init__.py
    init_file = base_dir / "__init__.py"
    if not init_file.exists():
        with open(init_file, 'w') as f:
            f.write('"""ArroyoUniversity Test Suite"""')
    
    print("\n✅ Test folder reorganization complete!")
    print("\nNew structure:")
    print("tests/")
    print("├── unit/")
    print("│   └── sprint1/          # Sprint 1 unit tests")
    print("├── integration/          # Integration tests")
    print("├── e2e/                  # End-to-end tests")
    print("├── performance/          # Performance tests")
    print("├── security/             # Security tests")
    print("├── runners/              # Test runners and validators")
    print("├── config/               # Test configuration")
    print("├── utils/                # Test utilities")
    print("├── fixtures/             # Test data and fixtures")
    print("└── docs/                 # Test documentation")

if __name__ == "__main__":
    reorganize_tests()
