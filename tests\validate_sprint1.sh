#!/bin/bash

# Sprint 1 Smart Validation Script
# This script performs intelligent validation of Sprint 1 implementation
# with different modes for different contexts.

# Configuration
VALIDATION_MODE="${VALIDATION_MODE:-quick}"
SILENT_MODE="${SILENT_MODE:-false}"
CONTINUE_ON_ERROR="${CONTINUE_ON_ERROR:-false}"

# Set error handling based on mode
if [ "$CONTINUE_ON_ERROR" = "true" ]; then
    set +e  # Don't exit on error
else
    set -e  # Exit on any error
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    if [ "$SILENT_MODE" != "true" ]; then
        echo -e "${GREEN}[✓]${NC} $1"
    fi
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_warning() {
    if [ "$SILENT_MODE" != "true" ]; then
        echo -e "${YELLOW}[!]${NC} $1"
    fi
}

print_info() {
    if [ "$SILENT_MODE" != "true" ]; then
        echo -e "${BLUE}[i]${NC} $1"
    fi
}

print_header() {
    if [ "$SILENT_MODE" != "true" ]; then
        echo -e "\n${BOLD}${BLUE}=== $1 ===${NC}\n"
    fi
}

print_summary() {
    echo -e "${BOLD}$1${NC}"
}

# Track validation results
VALIDATION_ERRORS=0

# Function to increment error count
add_error() {
    VALIDATION_ERRORS=$((VALIDATION_ERRORS + 1))
    print_error "$1"
}

# Function to validate a condition
validate() {
    if [ $1 -eq 0 ]; then
        print_status "$2"
    else
        add_error "$3"
    fi
}

# Show validation mode
case "$VALIDATION_MODE" in
    "quick")
        print_header "Sprint 1 Quick Validation"
        print_info "Fast validation for development workflow..."
        ;;
    "startup")
        print_header "Sprint 1 Startup Validation"
        print_info "Validating system readiness for development..."
        ;;
    "full")
        print_header "Sprint 1 Full Validation"
        print_info "Comprehensive validation of all components..."
        ;;
    *)
        print_header "Sprint 1 Validation"
        print_info "Validating Sprint 1 implementation..."
        ;;
esac

# 1. Check Docker Environment
print_header "1. Docker Environment Validation"

# Check if Docker is running
if command -v docker &> /dev/null; then
    if docker info &> /dev/null; then
        print_status "Docker is running"
    else
        add_error "Docker is installed but not running"
    fi
else
    add_error "Docker is not installed"
fi

# Check if Docker Compose is available
if command -v docker-compose &> /dev/null; then
    print_status "Docker Compose is available"
else
    add_error "Docker Compose is not installed"
fi

# Check if docker-compose.yml exists
if [ -f "docker-compose.yml" ]; then
    print_status "docker-compose.yml found"
else
    add_error "docker-compose.yml not found in project root"
fi

# Check if services are running
if docker-compose ps | grep -q "Up"; then
    print_status "Docker Compose services are running"
    
    # Check specific services
    if docker-compose ps postgres | grep -q "Up"; then
        print_status "PostgreSQL service is running"
    else
        add_error "PostgreSQL service is not running"
    fi
    
    if docker-compose ps redis | grep -q "Up"; then
        print_status "Redis service is running"
    else
        add_error "Redis service is not running"
    fi
    
else
    print_warning "Docker Compose services are not running. Starting them..."
    if docker-compose up -d; then
        print_status "Docker Compose services started"
        sleep 10  # Wait for services to be ready
    else
        add_error "Failed to start Docker Compose services"
    fi
fi

# 2. Check Project Structure
print_header "2. Project Structure Validation"

# Check for required directories
required_dirs=(
    "backend"
    "frontend" 
    "tests"
    "documentation"
)

for dir in "${required_dirs[@]}"; do
    if [ -d "$dir" ]; then
        print_status "Directory $dir exists"
    else
        add_error "Directory $dir is missing"
    fi
done

# Check for required files
required_files=(
    "Makefile"
    "backend/requirements.txt"
    "backend/Dockerfile.dev"
    "frontend/Dockerfile.dev"
    "tests/README.md"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        print_status "File $file exists"
    else
        add_error "File $file is missing"
    fi
done

# 3. Check Environment Configuration
print_header "3. Environment Configuration Validation"

# Check for environment file
if [ -f ".env" ] || [ -f ".env.example" ] || [ -f ".env.template" ]; then
    print_status "Environment configuration file found"
else
    add_error "No environment configuration file found (.env, .env.example, or .env.template)"
fi

# Check for required environment variables (if .env exists)
if [ -f ".env" ]; then
    required_vars=(
        "DATABASE_URL"
        "SECRET_KEY"
        "ENVIRONMENT"
    )
    
    for var in "${required_vars[@]}"; do
        if grep -q "^$var=" .env; then
            print_status "Environment variable $var is set"
        else
            add_error "Environment variable $var is not set in .env"
        fi
    done
fi

# 4. Check Database Connectivity
print_header "4. Database Connectivity Validation"

# Test PostgreSQL connectivity
if docker-compose exec -T postgres pg_isready -U postgres &> /dev/null; then
    print_status "PostgreSQL is accepting connections"
    
    # Test database exists
    if docker-compose exec -T postgres psql -U postgres -lqt | cut -d \| -f 1 | grep -qw arroyo_university; then
        print_status "Database 'arroyo_university' exists"
    else
        print_warning "Database 'arroyo_university' does not exist"
    fi
    
else
    add_error "PostgreSQL is not accepting connections"
fi

# Test Redis connectivity
if docker-compose exec -T redis redis-cli ping | grep -q "PONG"; then
    print_status "Redis is responding"
else
    add_error "Redis is not responding"
fi

# 5. Check Backend API
print_header "5. Backend API Validation"

# Wait a moment for backend to be ready
sleep 5

# Test backend health endpoint
if curl -f -s http://localhost:8000/health > /dev/null; then
    print_status "Backend health endpoint is responding"
    
    # Test API root endpoint
    if curl -f -s http://localhost:8000/ > /dev/null; then
        print_status "Backend root endpoint is responding"
    else
        add_error "Backend root endpoint is not responding"
    fi
    
    # Test API documentation
    if curl -f -s http://localhost:8000/docs > /dev/null; then
        print_status "API documentation is accessible"
    else
        add_error "API documentation is not accessible"
    fi
    
else
    add_error "Backend health endpoint is not responding"
fi

# 6. Check Database Schema
print_header "6. Database Schema Validation"

# Check if required tables exist
required_tables=(
    "tenants"
    "users"
)

for table in "${required_tables[@]}"; do
    if docker-compose exec -T postgres psql -U postgres -d arroyo_university -c "\dt" | grep -q "$table"; then
        print_status "Table '$table' exists"
    else
        add_error "Table '$table' does not exist"
    fi
done

# Check if RLS functions exist
rls_functions=(
    "current_tenant_id"
    "set_tenant_context"
)

for func in "${rls_functions[@]}"; do
    if docker-compose exec -T postgres psql -U postgres -d arroyo_university -c "\df" | grep -q "$func"; then
        print_status "RLS function '$func' exists"
    else
        add_error "RLS function '$func' does not exist"
    fi
done

# 7. Check Test Framework
print_header "7. Test Framework Validation"

# Check if pytest is available
if python -m pytest --version &> /dev/null; then
    print_status "Pytest is available"
else
    add_error "Pytest is not available"
fi

# Check if test files exist
test_files=(
    "tests/sprint-1/test_infrastructure.py"
    "tests/sprint-1/test_multi_tenant.py"
    "tests/sprint-1/test_database_rls.py"
)

for test_file in "${test_files[@]}"; do
    if [ -f "$test_file" ]; then
        print_status "Test file $test_file exists"
    else
        add_error "Test file $test_file is missing"
    fi
done

# 8. Mode-specific additional checks
case "$VALIDATION_MODE" in
    "startup"|"full")
        print_header "Additional Startup Checks"

        # Test API endpoints if in startup/full mode
        if curl -f -s http://localhost:8000/health > /dev/null 2>&1; then
            print_status "API health endpoint responding"

            # Test API documentation
            if curl -f -s http://localhost:8000/docs > /dev/null 2>&1; then
                print_status "API documentation accessible"
            else
                add_error "API documentation not accessible"
            fi
        fi

        # Run quick acceptance tests if in full mode
        if [ "$VALIDATION_MODE" = "full" ] && command -v python &> /dev/null; then
            print_info "Running quick acceptance tests..."
            if python -m pytest tests/sprint-1/test_infrastructure.py::TestAcceptanceCriteria -q > /dev/null 2>&1; then
                print_status "Acceptance criteria tests passed"
            else
                add_error "Acceptance criteria tests failed"
            fi
        fi
        ;;
esac

# 9. Summary
print_header "Validation Summary"

# Calculate success rate
TOTAL_CHECKS=$((VALIDATION_ERRORS + $(grep -c "print_status" <<< "$(set)" || echo "0")))
SUCCESS_RATE=$(( (TOTAL_CHECKS - VALIDATION_ERRORS) * 100 / TOTAL_CHECKS ))

if [ $VALIDATION_ERRORS -eq 0 ]; then
    print_summary "✅ All validations passed! (100% success rate)"

    case "$VALIDATION_MODE" in
        "quick")
            print_summary "🚀 System ready for development!"
            ;;
        "startup")
            print_summary "🎉 Sprint 1 startup validation complete!"
            print_info "📊 API Documentation: http://localhost:8000/docs"
            print_info "🏥 Health Check: http://localhost:8000/health"
            ;;
        "full")
            print_summary "🎉 Sprint 1 is fully validated and ready!"
            print_info "📊 API Documentation: http://localhost:8000/docs"
            print_info "🏥 Health Check: http://localhost:8000/health"
            print_info "🧪 Run 'make test' for comprehensive testing"
            ;;
    esac

    if [ "$SILENT_MODE" != "true" ]; then
        echo -e "\n${BLUE}Next steps:${NC}"
        echo "1. Run full test suite: python tests/run_sprint1_tests.py"
        echo "2. Run specific tests: pytest tests/sprint-1/ -v"
        echo "3. Check test coverage: pytest tests/sprint-1/ --cov=backend/src"
        echo "4. Proceed to Sprint 2 implementation"
    fi
    exit 0
else
    print_summary "❌ Found $VALIDATION_ERRORS validation errors (${SUCCESS_RATE}% success rate)"

    if [ "$CONTINUE_ON_ERROR" = "true" ]; then
        print_warning "Continuing despite errors (CONTINUE_ON_ERROR=true)"
        exit 0
    else
        if [ "$SILENT_MODE" != "true" ]; then
            echo -e "\n${YELLOW}Recommended actions:${NC}"
            echo "1. Review the errors above and fix them"
            echo "2. Check the implementation guide: documentation/devplan/sprint-1-infrastructure.md"
            echo "3. Ensure Docker services are running: docker-compose up -d"
            echo "4. Run setup script: make setup"
            echo "5. Re-run this validation: ./tests/validate_sprint1.sh"
        fi
        exit 1
    fi
fi
