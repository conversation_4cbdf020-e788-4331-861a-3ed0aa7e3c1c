"""
Question and question bank models
"""

from datetime import datetime
from typing import Optional, List, Any
from uuid import UUID
from enum import Enum

from sqlmodel import SQLModel, Field, Column
from sqlalchemy.dialects.postgresql import JSONB

from .base import TimestampMixin, TenantMixin, MetadataMixin


class QuestionType(str, Enum):
    """Question type enumeration"""
    MULTIPLE_CHOICE = "multiple_choice"
    TRUE_FALSE = "true_false"
    ESSAY = "essay"
    FILL_BLANK = "fill_blank"
    MULTIMEDIA = "multimedia"
    MATCHING = "matching"
    ORDERING = "ordering"


class DifficultyLevel(str, Enum):
    """Difficulty level enumeration"""
    EASY = "easy"
    MEDIUM = "medium"
    HARD = "hard"


class QuestionBankBase(SQLModel):
    """Base question bank model"""
    name: str = Field(max_length=255)
    description: Optional[str] = None
    subject_area: Optional[str] = Field(default=None, max_length=255)
    is_public: bool = Field(default=False)


class QuestionBank(QuestionBankBase, TenantMixin, TimestampMixin, MetadataMixin, table=True):
    """Question bank table model"""
    __tablename__ = "question_banks"
    
    bank_id: UUID = Field(primary_key=True)
    created_by: UUID = Field(foreign_key="users.user_id", index=True)
    difficulty_level: Optional[DifficultyLevel] = None


class QuestionBankCreate(QuestionBankBase):
    """Question bank creation model"""
    pass


class QuestionBankUpdate(SQLModel):
    """Question bank update model"""
    name: Optional[str] = Field(default=None, max_length=255)
    description: Optional[str] = None
    subject_area: Optional[str] = Field(default=None, max_length=255)
    is_public: Optional[bool] = None
    difficulty_level: Optional[DifficultyLevel] = None


class QuestionBankResponse(QuestionBankBase):
    """Question bank response model"""
    bank_id: UUID
    created_by: UUID
    difficulty_level: Optional[DifficultyLevel] = None
    created_at: datetime
    updated_at: datetime
    question_count: Optional[int] = None
    creator_name: Optional[str] = None


class QuestionBase(SQLModel):
    """Base question model"""
    title: str = Field(max_length=255)
    content: str
    question_type: QuestionType
    points: int = Field(default=1, ge=0)
    difficulty: DifficultyLevel = Field(default=DifficultyLevel.MEDIUM)
    category: Optional[str] = Field(default=None, max_length=100)
    time_limit_seconds: Optional[int] = Field(default=None, ge=0)
    explanation: Optional[str] = None


class Question(QuestionBase, TenantMixin, TimestampMixin, table=True):
    """Question table model"""
    __tablename__ = "questions"
    
    question_id: UUID = Field(primary_key=True)
    bank_id: Optional[UUID] = Field(foreign_key="question_banks.bank_id", default=None, index=True)
    created_by: UUID = Field(foreign_key="users.user_id", index=True)
    question_data: dict = Field(sa_column=Column(JSONB))  # Flexible question data
    tags: Optional[List[str]] = Field(default_factory=list, sa_column=Column(JSONB))
    is_active: bool = Field(default=True)
    usage_count: int = Field(default=0)
    correct_rate: Optional[float] = Field(default=None, ge=0.0, le=1.0)
    average_time_seconds: Optional[float] = Field(default=None, ge=0.0)


class QuestionCreate(QuestionBase):
    """Question creation model"""
    bank_id: Optional[UUID] = None
    question_data: dict
    tags: Optional[List[str]] = Field(default_factory=list)


class QuestionUpdate(SQLModel):
    """Question update model"""
    title: Optional[str] = Field(default=None, max_length=255)
    content: Optional[str] = None
    question_type: Optional[QuestionType] = None
    points: Optional[int] = Field(default=None, ge=0)
    difficulty: Optional[DifficultyLevel] = None
    category: Optional[str] = Field(default=None, max_length=100)
    time_limit_seconds: Optional[int] = Field(default=None, ge=0)
    explanation: Optional[str] = None
    question_data: Optional[dict] = None
    tags: Optional[List[str]] = None
    is_active: Optional[bool] = None


class QuestionResponse(QuestionBase):
    """Question response model"""
    question_id: UUID
    bank_id: Optional[UUID] = None
    created_by: UUID
    question_data: dict
    tags: List[str]
    is_active: bool
    usage_count: int
    correct_rate: Optional[float] = None
    average_time_seconds: Optional[float] = None
    created_at: datetime
    updated_at: datetime
    creator_name: Optional[str] = None
    bank_name: Optional[str] = None


class QuestionStatistics(SQLModel):
    """Question statistics model"""
    question_id: UUID
    total_attempts: int
    correct_attempts: int
    correct_rate: float
    average_time_seconds: float
    difficulty_rating: float
    usage_count: int
    last_used: Optional[datetime] = None


class AIQuestionGeneration(SQLModel):
    """AI question generation request model"""
    topic: str = Field(max_length=255)
    question_type: QuestionType
    difficulty: DifficultyLevel
    count: int = Field(ge=1, le=20)
    language: str = Field(default="en", max_length=10)
    additional_instructions: Optional[str] = Field(default=None, max_length=1000)
    bank_id: Optional[UUID] = None


class AIQuestionGenerationResponse(SQLModel):
    """AI question generation response model"""
    task_id: str
    status: str
    questions_generated: Optional[int] = None
    questions: Optional[List[QuestionResponse]] = None
    error: Optional[str] = None


class QuestionImport(SQLModel):
    """Question import model"""
    format: str = Field(max_length=20)  # 'csv', 'json', 'qti'
    bank_id: Optional[UUID] = None
    questions_data: List[dict]


class QuestionImportResult(SQLModel):
    """Question import result model"""
    imported_count: int
    failed_count: int
    errors: List[str]
    questions: List[QuestionResponse]


class QuestionExport(SQLModel):
    """Question export model"""
    question_ids: List[UUID]
    format: str = Field(default="json", max_length=20)
    include_statistics: bool = Field(default=False)


class QuestionValidation(SQLModel):
    """Question validation model"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    suggestions: Optional[List[str]] = None


class QuestionRecommendation(SQLModel):
    """Question recommendation model"""
    course_id: UUID
    count: int = Field(default=10, ge=1, le=50)
    difficulty_preference: Optional[DifficultyLevel] = None
    category_preference: Optional[str] = None
