"""
Backend core security - compatibility import from core-api
"""

import sys
import os

# Add core-api to path
core_api_path = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'core-api')
sys.path.insert(0, core_api_path)

# Import from actual implementation
try:
    from app.core.security import *
except ImportError:
    # Fallback for testing
    import hashlib
    import secrets
    
    def get_password_hash(password: str) -> str:
        """Hash a password for storing."""
        salt = secrets.token_hex(16)
        return hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000).hex() + ':' + salt
    
    def create_access_token(data: dict) -> str:
        """Create access token."""
        return f"mock_token_{secrets.token_hex(16)}"
