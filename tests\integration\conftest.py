"""
Integration test configuration
"""

import pytest
import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Setup backend mocks if backend is not available
try:
    import backend
except ImportError:
    # Import mocks to setup sys.modules
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'mocks'))
    import backend_mock

# Test database configuration
TEST_DATABASE_URL = os.getenv(
    "TEST_DATABASE_URL", 
    "postgresql://postgres:postgres123@localhost:5432/arroyo_university"
)


@pytest.fixture(scope="session")
def test_engine():
    """Create test database engine for the session."""
    engine = create_engine(TEST_DATABASE_URL, echo=False)
    
    # Test connection
    try:
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
    except Exception as e:
        pytest.skip(f"Database not available: {e}")
    
    yield engine
    engine.dispose()


@pytest.fixture(scope="session")
def test_session_factory(test_engine):
    """Create test session factory."""
    return sessionmaker(bind=test_engine)


@pytest.fixture
def test_db(test_session_factory):
    """Create test database session."""
    session = test_session_factory()
    try:
        yield session
    finally:
        session.rollback()
        session.close()


@pytest.fixture
def sample_tenant_data():
    """Sample tenant data for testing."""
    return {
        'tenant_id': '550e8400-e29b-41d4-a716-446655440000',
        'name': 'Test University',
        'subdomain': 'test-university',
        'description': 'A test university for integration testing',
        'status': 'active',
        'plan': 'basic',
        'settings': '{"theme": "blue", "max_users": 100}'
    }


@pytest.fixture
def sample_user_data():
    """Sample user data for testing."""
    return {
        'user_id': '550e8400-e29b-41d4-a716-446655440001',
        'tenant_id': '550e8400-e29b-41d4-a716-446655440000',
        'email': '<EMAIL>',
        'username': 'testuser',
        'password_hash': 'hashed_password_123',
        'first_name': 'Test',
        'last_name': 'User',
        'status': 'active',
        'email_verified': True
    }
