"""
Backend core tenant context - compatibility import from core-api
"""

import sys
import os

# Add core-api to path
core_api_path = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'core-api')
sys.path.insert(0, core_api_path)

# Import from actual implementation
try:
    from app.core.tenant_context import *
except ImportError:
    # Fallback for testing
    from contextlib import contextmanager
    
    @contextmanager
    def tenant_context(db, tenant_id):
        """Tenant context manager."""
        yield {"tenant_id": tenant_id, "db": db}
