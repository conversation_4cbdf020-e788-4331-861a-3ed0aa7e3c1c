-- Database migrations for all Sprint 1-3 tables
-- Run this after completing all three sprints

-- Enable extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create RLS functions (from Sprint 1)
CREATE OR REPLACE FUNCTION current_tenant_id() RETURNS TEXT AS $$
BEGIN
    RETURN current_setting('app.current_tenant_id', true);
END;
$$ LANGUAGE plpgsql STABLE;

CREATE OR REPLACE FUNCTION set_tenant_context(tenant_id TEXT) RETURNS VOID AS $$
BEGIN
    PERFORM set_config('app.current_tenant_id', tenant_id, false);
END;
$$ LANGUAGE plpgsql;

-- Create tenant isolation policy function
CREATE OR REPLACE FUNCTION tenant_isolation_policy(tenant_id TEXT) RETURNS BOOLEAN AS $$
BEGIN
    RETURN tenant_id = current_tenant_id();
END;
$$ LANGUAGE plpgsql STABLE;

-- Create tenants table (Sprint 1)
CREATE TABLE IF NOT EXISTS tenants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    domain VARCHAR(255) UNIQUE,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    settings JSONB DEFAULT '{}',
    logo_url VARCHAR(500),
    primary_color VARCHAR(7) DEFAULT '#007bff',
    secondary_color VARCHAR(7) DEFAULT '#6c757d',
    plan VARCHAR(50) DEFAULT 'free',
    max_users INTEGER DEFAULT 100,
    contact_email VARCHAR(255),
    contact_phone VARCHAR(50),
    address TEXT,
    is_deleted BOOLEAN DEFAULT FALSE NOT NULL,
    deleted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create users table (Sprint 2)
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    username VARCHAR(100),
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    hashed_password VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    is_verified BOOLEAN DEFAULT FALSE NOT NULL,
    role VARCHAR(50) DEFAULT 'user' NOT NULL,
    last_login TIMESTAMP WITH TIME ZONE,
    login_count INTEGER DEFAULT 0,
    avatar_url VARCHAR(500),
    bio VARCHAR(500),
    timezone VARCHAR(50) DEFAULT 'UTC',
    language VARCHAR(10) DEFAULT 'en',
    is_deleted BOOLEAN DEFAULT FALSE NOT NULL,
    deleted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    UNIQUE(tenant_id, email),
    UNIQUE(tenant_id, username)
);

-- Create invitations table (Sprint 2)
CREATE TABLE IF NOT EXISTS invitations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    token VARCHAR(255) UNIQUE NOT NULL,
    role VARCHAR(50) DEFAULT 'user' NOT NULL,
    is_used BOOLEAN DEFAULT FALSE NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    invited_by UUID NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create question type enum
CREATE TYPE question_type AS ENUM (
    'multiple_choice', 'true_false', 'short_answer', 'essay', 
    'fill_blank', 'matching', 'ordering', 'listening', 
    'speaking', 'reading', 'writing'
);

-- Create difficulty level enum
CREATE TYPE difficulty_level AS ENUM (
    'beginner', 'elementary', 'intermediate', 
    'upper_intermediate', 'advanced', 'proficient'
);

-- Create questions table (Sprint 3)
CREATE TABLE IF NOT EXISTS questions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    title VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    question_type question_type NOT NULL,
    difficulty_level difficulty_level NOT NULL,
    subject VARCHAR(100),
    topic VARCHAR(200),
    options JSONB,
    correct_answer JSONB,
    explanation TEXT,
    points INTEGER DEFAULT 1 NOT NULL,
    time_limit INTEGER,
    audio_url VARCHAR(500),
    image_url VARCHAR(500),
    video_url VARCHAR(500),
    language VARCHAR(10) DEFAULT 'en' NOT NULL,
    version INTEGER DEFAULT 1 NOT NULL,
    is_published BOOLEAN DEFAULT FALSE NOT NULL,
    is_ai_generated BOOLEAN DEFAULT FALSE NOT NULL,
    ai_prompt TEXT,
    ai_model VARCHAR(100),
    quality_score INTEGER,
    review_status VARCHAR(50) DEFAULT 'pending' NOT NULL,
    usage_count INTEGER DEFAULT 0 NOT NULL,
    success_rate INTEGER,
    created_by UUID NOT NULL,
    reviewed_by UUID,
    is_deleted BOOLEAN DEFAULT FALSE NOT NULL,
    deleted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create tags table (Sprint 3)
CREATE TABLE IF NOT EXISTS tags (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#007bff' NOT NULL,
    parent_id UUID REFERENCES tags(id) ON DELETE SET NULL,
    category VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create question_tags junction table (Sprint 3)
CREATE TABLE IF NOT EXISTS question_tags (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    question_id UUID NOT NULL REFERENCES questions(id) ON DELETE CASCADE,
    tag_id UUID NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    UNIQUE(question_id, tag_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_tenants_slug ON tenants(slug);
CREATE INDEX IF NOT EXISTS idx_tenants_domain ON tenants(domain);
CREATE INDEX IF NOT EXISTS idx_tenants_active ON tenants(is_active) WHERE is_active = TRUE;

CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(tenant_id, email);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(tenant_id, username);
CREATE INDEX IF NOT EXISTS idx_users_active ON users(tenant_id, is_active) WHERE is_active = TRUE;

CREATE INDEX IF NOT EXISTS idx_invitations_tenant_id ON invitations(tenant_id);
CREATE INDEX IF NOT EXISTS idx_invitations_token ON invitations(token);
CREATE INDEX IF NOT EXISTS idx_invitations_email ON invitations(tenant_id, email);

CREATE INDEX IF NOT EXISTS idx_questions_tenant_id ON questions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_questions_type ON questions(tenant_id, question_type);
CREATE INDEX IF NOT EXISTS idx_questions_difficulty ON questions(tenant_id, difficulty_level);
CREATE INDEX IF NOT EXISTS idx_questions_subject ON questions(tenant_id, subject);
CREATE INDEX IF NOT EXISTS idx_questions_topic ON questions(tenant_id, topic);
CREATE INDEX IF NOT EXISTS idx_questions_published ON questions(tenant_id, is_published) WHERE is_published = TRUE;
CREATE INDEX IF NOT EXISTS idx_questions_created_by ON questions(tenant_id, created_by);

CREATE INDEX IF NOT EXISTS idx_tags_tenant_id ON tags(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(tenant_id, name);
CREATE INDEX IF NOT EXISTS idx_tags_category ON tags(tenant_id, category);
CREATE INDEX IF NOT EXISTS idx_tags_parent ON tags(parent_id);

CREATE INDEX IF NOT EXISTS idx_question_tags_question ON question_tags(question_id);
CREATE INDEX IF NOT EXISTS idx_question_tags_tag ON question_tags(tag_id);
CREATE INDEX IF NOT EXISTS idx_question_tags_tenant ON question_tags(tenant_id);

-- Enable RLS on tenant-specific tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE question_tags ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY tenant_isolation_policy ON users
    FOR ALL TO PUBLIC
    USING (tenant_id::TEXT = current_tenant_id());

CREATE POLICY tenant_isolation_policy ON invitations
    FOR ALL TO PUBLIC
    USING (tenant_id::TEXT = current_tenant_id());

CREATE POLICY tenant_isolation_policy ON questions
    FOR ALL TO PUBLIC
    USING (tenant_id::TEXT = current_tenant_id());

CREATE POLICY tenant_isolation_policy ON tags
    FOR ALL TO PUBLIC
    USING (tenant_id::TEXT = current_tenant_id());

CREATE POLICY tenant_isolation_policy ON question_tags
    FOR ALL TO PUBLIC
    USING (tenant_id::TEXT = current_tenant_id());

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON tenants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_invitations_updated_at BEFORE UPDATE ON invitations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_questions_updated_at BEFORE UPDATE ON questions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tags_updated_at BEFORE UPDATE ON tags
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_question_tags_updated_at BEFORE UPDATE ON question_tags
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
