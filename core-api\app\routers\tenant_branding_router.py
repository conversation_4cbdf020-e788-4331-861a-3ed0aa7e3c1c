"""
Tenant Branding Configuration Router
Handles tenant-specific branding and customization
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status, UploadFile, File
from fastapi.responses import Response
from sqlmodel import Session
from typing import List, Optional, Dict, Any
from uuid import UUID

from ..core.database import get_session
from ..core.security import get_current_user, require_permissions
from ..models.user import User
from ..models.tenant_branding import (
    TenantBranding, TenantBrandingCreate, TenantBrandingUpdate,
    TenantBrandingResponse, TenantBrandingHistory, SubdomainAvailability,
    BrandingValidation, DEFAULT_BRANDING
)
from ..services.tenant_branding_service import TenantBrandingService
from ..services.storage_service import get_storage_service
from ..core.metrics import record_file_upload

router = APIRouter()

# Branding Configuration
@router.get("/", response_model=Optional[TenantBrandingResponse])
async def get_tenant_branding(
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["branding_view"]))
):
    """Get current tenant branding configuration"""
    service = TenantBrandingService(session)
    branding = await service.get_branding(current_user.tenant_id)
    return branding

@router.post("/", response_model=TenantBrandingResponse, status_code=status.HTTP_201_CREATED)
async def create_tenant_branding(
    branding_data: TenantBrandingCreate,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["branding_manage"]))
):
    """Create tenant branding configuration"""
    service = TenantBrandingService(session)
    branding = await service.create_branding(
        tenant_id=current_user.tenant_id,
        branding_data=branding_data,
        user_id=current_user.user_id
    )
    return branding

@router.put("/", response_model=TenantBrandingResponse)
async def update_tenant_branding(
    branding_data: TenantBrandingUpdate,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["branding_manage"]))
):
    """Update tenant branding configuration"""
    service = TenantBrandingService(session)
    branding = await service.update_branding(
        tenant_id=current_user.tenant_id,
        branding_data=branding_data,
        user_id=current_user.user_id
    )
    return branding

@router.delete("/", status_code=status.HTTP_204_NO_CONTENT)
async def delete_tenant_branding(
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["branding_manage"]))
):
    """Delete tenant branding configuration"""
    service = TenantBrandingService(session)
    success = await service.delete_branding(current_user.tenant_id, current_user.user_id)
    if not success:
        raise HTTPException(status_code=404, detail="Branding configuration not found")

# Subdomain Management
@router.get("/subdomain/check/{subdomain}", response_model=SubdomainAvailability)
async def check_subdomain_availability(
    subdomain: str,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["branding_view"]))
):
    """Check if subdomain is available"""
    service = TenantBrandingService(session)
    is_available = await service.is_subdomain_available(subdomain, current_user.tenant_id)
    
    suggestions = []
    if not is_available:
        suggestions = await service.get_subdomain_suggestions(subdomain)
    
    return SubdomainAvailability(
        subdomain=subdomain,
        is_available=is_available,
        suggestions=suggestions
    )

@router.get("/subdomain/suggestions/{base_name}", response_model=List[str])
async def get_subdomain_suggestions(
    base_name: str,
    count: int = Query(5, ge=1, le=10),
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["branding_view"]))
):
    """Get subdomain suggestions based on base name"""
    service = TenantBrandingService(session)
    return await service.get_subdomain_suggestions(base_name, count)

# Validation
@router.post("/validate", response_model=BrandingValidation)
async def validate_branding_config(
    branding_data: Dict[str, Any],
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["branding_view"]))
):
    """Validate branding configuration"""
    service = TenantBrandingService(session)
    return await service.validate_branding(branding_data)

# CSS Generation
@router.get("/css", response_class=Response)
async def get_tenant_css(
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Get generated CSS for tenant branding"""
    service = TenantBrandingService(session)
    css_content = await service.generate_css(current_user.tenant_id)
    
    return Response(
        content=css_content,
        media_type="text/css",
        headers={
            "Cache-Control": "public, max-age=3600",
            "Content-Disposition": "inline; filename=tenant-branding.css"
        }
    )

# History
@router.get("/history", response_model=List[TenantBrandingHistory])
async def get_branding_history(
    limit: int = Query(10, ge=1, le=50),
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["branding_view"]))
):
    """Get branding change history"""
    service = TenantBrandingService(session)
    return await service.get_branding_history(current_user.tenant_id, limit)

# File Upload Endpoints
@router.post("/upload/logo", response_model=Dict[str, str])
async def upload_logo(
    file: UploadFile = File(...),
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["branding_manage"]))
):
    """Upload logo file"""
    # Validate file type
    if not file.content_type or not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")

    # Validate file size (5MB max)
    if file.size and file.size > 5 * 1024 * 1024:
        raise HTTPException(status_code=400, detail="File size must be less than 5MB")

    # Upload to storage service
    storage_service = get_storage_service()
    try:
        result = await storage_service.upload_logo(file, str(current_user.tenant_id))

        # Record metrics
        record_file_upload("logo", "success", result["size"])

        return {
            "url": result["url"],
            "filename": result["filename"],
            "size": result["size"]
        }
    except Exception as e:
        record_file_upload("logo", "error", 0)
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

@router.post("/upload/favicon", response_model=Dict[str, str])
async def upload_favicon(
    file: UploadFile = File(...),
    current_user: User = Depends(require_permissions(["branding_manage"]))
):
    """Upload favicon file"""
    # Validate file type
    if not file.content_type or not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")

    # Validate file size (1MB max for favicon)
    if file.size and file.size > 1 * 1024 * 1024:
        raise HTTPException(status_code=400, detail="Favicon size must be less than 1MB")

    # Upload to storage service
    storage_service = get_storage_service()
    try:
        result = await storage_service.upload_favicon(file, str(current_user.tenant_id))

        # Record metrics
        record_file_upload("favicon", "success", result["size"])

        return {
            "url": result["url"],
            "filename": result["filename"],
            "size": result["size"]
        }
    except Exception as e:
        record_file_upload("favicon", "error", 0)
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

@router.post("/upload/background", response_model=Dict[str, str])
async def upload_background(
    file: UploadFile = File(...),
    current_user: User = Depends(require_permissions(["branding_manage"]))
):
    """Upload background image file"""
    # Validate file type
    if not file.content_type or not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")

    # Validate file size (10MB max for background)
    if file.size and file.size > 10 * 1024 * 1024:
        raise HTTPException(status_code=400, detail="Background image size must be less than 10MB")

    # Upload to storage service
    storage_service = get_storage_service()
    try:
        result = await storage_service.upload_background(file, str(current_user.tenant_id))

        # Record metrics
        record_file_upload("background", "success", result["size"])

        return {
            "url": result["url"],
            "filename": result["filename"],
            "size": result["size"]
        }
    except Exception as e:
        record_file_upload("background", "error", 0)
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

# Default Configuration
@router.get("/defaults", response_model=Dict[str, Any])
async def get_default_branding(
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["branding_view"]))
):
    """Get default branding configuration"""
    return DEFAULT_BRANDING

@router.post("/reset", response_model=TenantBrandingResponse)
async def reset_to_defaults(
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["branding_manage"]))
):
    """Reset branding to default configuration"""
    service = TenantBrandingService(session)
    
    # Create branding data from defaults
    branding_data = TenantBrandingCreate(**DEFAULT_BRANDING)
    
    # Update existing branding or create new one
    existing_branding = await service.get_branding(current_user.tenant_id)
    if existing_branding:
        update_data = TenantBrandingUpdate(**DEFAULT_BRANDING)
        branding = await service.update_branding(
            tenant_id=current_user.tenant_id,
            branding_data=update_data,
            user_id=current_user.user_id
        )
    else:
        branding = await service.create_branding(
            tenant_id=current_user.tenant_id,
            branding_data=branding_data,
            user_id=current_user.user_id
        )
    
    return branding

# Export/Import
@router.get("/export", response_class=Response)
async def export_branding_config(
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["branding_view"]))
):
    """Export branding configuration as JSON"""
    service = TenantBrandingService(session)
    branding = await service.get_branding(current_user.tenant_id)
    
    if not branding:
        raise HTTPException(status_code=404, detail="No branding configuration found")
    
    # Convert to dict and remove sensitive fields
    config_data = branding.model_dump()
    sensitive_fields = ["branding_id", "tenant_id", "applied_by", "created_at", "updated_at"]
    for field in sensitive_fields:
        config_data.pop(field, None)
    
    import json
    json_content = json.dumps(config_data, indent=2, default=str)
    
    return Response(
        content=json_content,
        media_type="application/json",
        headers={
            "Content-Disposition": f"attachment; filename=branding-config-{current_user.tenant_id}.json"
        }
    )

@router.post("/import", response_model=TenantBrandingResponse)
async def import_branding_config(
    file: UploadFile = File(...),
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["branding_manage"]))
):
    """Import branding configuration from JSON file"""
    # Validate file type
    if not file.filename or not file.filename.endswith('.json'):
        raise HTTPException(status_code=400, detail="File must be a JSON file")
    
    try:
        # Read and parse JSON
        content = await file.read()
        import json
        config_data = json.loads(content.decode('utf-8'))
        
        # Validate and create/update branding
        service = TenantBrandingService(session)
        
        # Check if branding exists
        existing_branding = await service.get_branding(current_user.tenant_id)
        
        if existing_branding:
            update_data = TenantBrandingUpdate(**config_data)
            branding = await service.update_branding(
                tenant_id=current_user.tenant_id,
                branding_data=update_data,
                user_id=current_user.user_id
            )
        else:
            create_data = TenantBrandingCreate(**config_data)
            branding = await service.create_branding(
                tenant_id=current_user.tenant_id,
                branding_data=create_data,
                user_id=current_user.user_id
            )
        
        return branding
        
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON file")
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Import failed: {str(e)}")

# Preview
@router.get("/preview-url", response_model=Dict[str, str])
async def get_preview_url(
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["branding_view"]))
):
    """Get preview URL for current branding"""
    service = TenantBrandingService(session)
    branding = await service.get_branding(current_user.tenant_id)
    
    if not branding or not branding.subdomain:
        raise HTTPException(status_code=404, detail="No subdomain configured")
    
    preview_url = f"https://{branding.subdomain}.arroyouniversity.com"
    
    return {
        "preview_url": preview_url,
        "subdomain": branding.subdomain
    }
