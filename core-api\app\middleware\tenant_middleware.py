"""
Tenant Context Middleware for Multi-tenant Architecture
Automatically sets tenant context for each request based on authentication
"""

import logging
from typing import Optional
from fastapi import Request, Response, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import <PERSON><PERSON><PERSON>esponse
from sqlalchemy.orm import Session

# JWT is optional for now
try:
    import jwt
    JWT_AVAILABLE = True
except ImportError:
    JWT_AVAILABLE = False

from app.core.database import get_session
from app.core.tenant_context import set_tenant_context_for_session, clear_tenant_context_for_session
from app.core.config import settings

logger = logging.getLogger(__name__)


class TenantContextMiddleware(BaseHTTPMiddleware):
    """Middleware to automatically set tenant context for each request"""

    def __init__(self, app, exclude_paths: Optional[list] = None):
        super().__init__(app)
        self.exclude_paths = exclude_paths or [
            "/health", 
            "/docs", 
            "/redoc", 
            "/openapi.json",
            "/auth/login",
            "/auth/register",
            "/",
            "/favicon.ico"
        ]

    async def dispatch(self, request: Request, call_next):
        """Process request and set tenant context"""
        
        # Skip tenant context for excluded paths
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)

        # Get database session
        db = next(get_session())
        
        try:
            # Extract tenant context from request
            tenant_id, user_id = await self._extract_tenant_context(request, db)
            
            if tenant_id:
                # Set tenant context for this request
                set_tenant_context_for_session(db, tenant_id, user_id)
                logger.debug(f"Set tenant context for request: tenant_id={tenant_id}, user_id={user_id}")
            
            # Process the request
            response = await call_next(request)
            
            return response
            
        except Exception as e:
            logger.error(f"Error in tenant context middleware: {e}")
            # Don't fail the request, just log the error
            return await call_next(request)
        
        finally:
            # Always clear tenant context after request
            try:
                clear_tenant_context_for_session(db)
            except:
                pass
            finally:
                db.close()

    async def _extract_tenant_context(self, request: Request, db: Session) -> tuple[Optional[str], Optional[str]]:
        """Extract tenant and user context from request"""
        
        # Method 1: From JWT token in Authorization header
        tenant_id, user_id = await self._extract_from_jwt(request)
        if tenant_id:
            return tenant_id, user_id
        
        # Method 2: From subdomain
        tenant_id = await self._extract_from_subdomain(request, db)
        if tenant_id:
            return tenant_id, None
        
        # Method 3: From X-Tenant-ID header (for API clients)
        tenant_id = await self._extract_from_header(request)
        if tenant_id:
            return tenant_id, None
        
        return None, None

    async def _extract_from_jwt(self, request: Request) -> tuple[Optional[str], Optional[str]]:
        """Extract tenant and user ID from JWT token"""
        if not JWT_AVAILABLE:
            return None, None

        try:
            # Get Authorization header
            auth_header = request.headers.get("Authorization")
            if not auth_header or not auth_header.startswith("Bearer "):
                return None, None

            # Extract token
            token = auth_header.split(" ")[1]

            # Decode JWT (without verification for now - should be verified in auth middleware)
            payload = jwt.decode(token, options={"verify_signature": False})

            tenant_id = payload.get("tenant_id")
            user_id = payload.get("sub") or payload.get("user_id")

            return str(tenant_id) if tenant_id else None, str(user_id) if user_id else None

        except Exception as e:
            logger.debug(f"Could not extract tenant context from JWT: {e}")
            return None, None

    async def _extract_from_subdomain(self, request: Request, db: Session) -> Optional[str]:
        """Extract tenant ID from subdomain"""
        try:
            # Get host from request
            host = request.headers.get("host", "")
            
            # Extract subdomain (assuming format: subdomain.domain.com)
            parts = host.split(".")
            if len(parts) >= 3:
                subdomain = parts[0]
                
                # Look up tenant by subdomain
                from app.models.tenant import Tenant
                tenant = db.query(Tenant).filter(Tenant.subdomain == subdomain).first()
                
                if tenant:
                    return str(tenant.tenant_id)
            
            return None
            
        except Exception as e:
            logger.debug(f"Could not extract tenant context from subdomain: {e}")
            return None

    async def _extract_from_header(self, request: Request) -> Optional[str]:
        """Extract tenant ID from X-Tenant-ID header"""
        try:
            tenant_id = request.headers.get("X-Tenant-ID")
            return str(tenant_id) if tenant_id else None
        except Exception as e:
            logger.debug(f"Could not extract tenant context from header: {e}")
            return None


class TenantValidationMiddleware(BaseHTTPMiddleware):
    """Middleware to validate tenant access and enforce tenant isolation"""

    def __init__(self, app, require_tenant: bool = True):
        super().__init__(app)
        self.require_tenant = require_tenant
        self.exclude_paths = [
            "/health", 
            "/docs", 
            "/redoc", 
            "/openapi.json",
            "/auth/login",
            "/auth/register",
            "/",
            "/favicon.ico"
        ]

    async def dispatch(self, request: Request, call_next):
        """Validate tenant context for request"""
        
        # Skip validation for excluded paths
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)

        # Skip validation if not required
        if not self.require_tenant:
            return await call_next(request)

        # Get database session
        db = next(get_session())
        
        try:
            # Check if tenant context is set
            from app.core.tenant_context import get_current_tenant_id
            tenant_id = get_current_tenant_id(db)
            
            if not tenant_id:
                return JSONResponse(
                    status_code=400,
                    content={
                        "type": "https://api.arroyo.app/errors/tenant-context-missing",
                        "title": "Tenant Context Missing",
                        "status": 400,
                        "detail": "Tenant context is required for this operation",
                        "instance": str(request.url)
                    }
                )
            
            # Validate tenant exists and is active
            from app.models.tenant import Tenant
            tenant = db.query(Tenant).filter(Tenant.tenant_id == tenant_id).first()
            
            if not tenant:
                return JSONResponse(
                    status_code=404,
                    content={
                        "type": "https://api.arroyo.app/errors/tenant-not-found",
                        "title": "Tenant Not Found",
                        "status": 404,
                        "detail": "The specified tenant does not exist",
                        "instance": str(request.url)
                    }
                )
            
            if tenant.status != "active":
                return JSONResponse(
                    status_code=403,
                    content={
                        "type": "https://api.arroyo.app/errors/tenant-inactive",
                        "title": "Tenant Inactive",
                        "status": 403,
                        "detail": "The tenant is not active",
                        "instance": str(request.url)
                    }
                )
            
            # Process the request
            response = await call_next(request)
            return response
            
        except Exception as e:
            logger.error(f"Error in tenant validation middleware: {e}")
            return JSONResponse(
                status_code=500,
                content={
                    "type": "https://api.arroyo.app/errors/tenant-validation-error",
                    "title": "Tenant Validation Error",
                    "status": 500,
                    "detail": "An error occurred while validating tenant context",
                    "instance": str(request.url)
                }
            )
        
        finally:
            db.close()
