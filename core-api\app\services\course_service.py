"""
Course management service
"""

from datetime import datetime
from typing import Optional, List
from uuid import UUID

from sqlmodel import Session, select, func
from sqlalchemy.orm import selectinload

from ..models.course import (
    Course, CourseCreate, CourseUpdate, CourseResponse, CourseStatus,
    CourseModule, CourseModuleCreate, CourseModuleUpdate,
    ContentItem, ContentItemCreate, ContentItemUpdate,
    CourseRating, CourseRatingCreate, ExpertReview, ExpertReviewCreate
)
from ..models.enrollment import CourseEnrollment, UserProgress
from ..models.user import User
from ..services.notification_service import NotificationService


class CourseService:
    """Course management service"""
    
    def __init__(self, db: Session):
        self.db = db
        self.notification_service = NotificationService(db)
    
    async def create_course(self, course_data: CourseCreate, instructor_id: UUID, tenant_id: UUID) -> Course:
        """Create a new course"""
        course = Course(
            tenant_id=tenant_id,
            instructor_id=instructor_id,
            title=course_data.title,
            description=course_data.description,
            short_description=course_data.short_description,
            category=course_data.category,
            difficulty_level=course_data.difficulty_level,
            estimated_duration_hours=course_data.estimated_duration_hours,
            thumbnail_url=course_data.thumbnail_url,
            is_public=course_data.is_public,
            price=course_data.price,
            currency=course_data.currency,
            tags=course_data.tags or [],
            learning_objectives=course_data.learning_objectives or [],
            prerequisites=course_data.prerequisites or []
        )
        
        self.db.add(course)
        self.db.commit()
        self.db.refresh(course)
        
        return course
    
    async def get_course(self, course_id: UUID, tenant_id: UUID) -> Optional[CourseResponse]:
        """Get course by ID with additional data"""
        stmt = select(Course).where(
            Course.course_id == course_id,
            Course.tenant_id == tenant_id
        )
        course = self.db.exec(stmt).first()
        
        if not course:
            return None
        
        # Get instructor name
        instructor = self.db.get(User, course.instructor_id)
        instructor_name = f"{instructor.first_name} {instructor.last_name}" if instructor else None
        
        # Get enrollment count
        enrollment_count = self.db.exec(
            select(func.count(CourseEnrollment.enrollment_id)).where(
                CourseEnrollment.course_id == course_id
            )
        ).first() or 0
        
        # Get completion rate
        total_enrollments = enrollment_count
        completed_enrollments = self.db.exec(
            select(func.count(CourseEnrollment.enrollment_id)).where(
                CourseEnrollment.course_id == course_id,
                CourseEnrollment.status == "completed"
            )
        ).first() or 0
        
        completion_rate = (completed_enrollments / total_enrollments * 100) if total_enrollments > 0 else 0
        
        # Get average rating
        avg_rating = self.db.exec(
            select(func.avg(CourseRating.rating)).where(
                CourseRating.course_id == course_id
            )
        ).first()
        
        return CourseResponse(
            course_id=course.course_id,
            instructor_id=course.instructor_id,
            title=course.title,
            description=course.description,
            short_description=course.short_description,
            category=course.category,
            difficulty_level=course.difficulty_level,
            estimated_duration_hours=course.estimated_duration_hours,
            thumbnail_url=course.thumbnail_url,
            is_public=course.is_public,
            price=course.price,
            currency=course.currency,
            status=course.status,
            created_at=course.created_at,
            updated_at=course.updated_at,
            published_at=course.published_at,
            tags=course.tags,
            learning_objectives=course.learning_objectives,
            prerequisites=course.prerequisites,
            enrollment_count=enrollment_count,
            completion_rate=completion_rate,
            average_rating=float(avg_rating) if avg_rating else None,
            instructor_name=instructor_name
        )
    
    async def update_course(self, course_id: UUID, course_data: CourseUpdate, tenant_id: UUID) -> Optional[Course]:
        """Update course"""
        course = self.db.exec(
            select(Course).where(
                Course.course_id == course_id,
                Course.tenant_id == tenant_id
            )
        ).first()
        
        if not course:
            return None
        
        # Update fields
        update_data = course_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(course, field, value)
        
        course.updated_at = datetime.utcnow()
        
        self.db.add(course)
        self.db.commit()
        self.db.refresh(course)
        
        return course
    
    async def delete_course(self, course_id: UUID, tenant_id: UUID) -> bool:
        """Delete course (only if no enrollments)"""
        course = self.db.exec(
            select(Course).where(
                Course.course_id == course_id,
                Course.tenant_id == tenant_id
            )
        ).first()
        
        if not course:
            return False
        
        # Check if course has enrollments
        enrollment_count = self.db.exec(
            select(func.count(CourseEnrollment.enrollment_id)).where(
                CourseEnrollment.course_id == course_id
            )
        ).first() or 0
        
        if enrollment_count > 0:
            raise ValueError("Cannot delete course with existing enrollments")
        
        self.db.delete(course)
        self.db.commit()
        
        return True
    
    async def publish_course(self, course_id: UUID, tenant_id: UUID) -> bool:
        """Publish course"""
        course = self.db.exec(
            select(Course).where(
                Course.course_id == course_id,
                Course.tenant_id == tenant_id
            )
        ).first()
        
        if not course:
            return False
        
        course.status = CourseStatus.PUBLISHED
        course.published_at = datetime.utcnow()
        course.updated_at = datetime.utcnow()
        
        self.db.add(course)
        self.db.commit()
        
        return True
    
    async def list_courses(
        self,
        tenant_id: UUID,
        skip: int = 0,
        limit: int = 20,
        search: str = None,
        category: str = None,
        difficulty: str = None,
        instructor_id: UUID = None,
        status: CourseStatus = None,
        is_public: bool = None
    ) -> List[CourseResponse]:
        """List courses with filtering"""
        stmt = select(Course).where(Course.tenant_id == tenant_id)
        
        # Apply filters
        if search:
            search_term = f"%{search}%"
            stmt = stmt.where(
                (Course.title.ilike(search_term)) |
                (Course.description.ilike(search_term)) |
                (Course.short_description.ilike(search_term))
            )
        
        if category:
            stmt = stmt.where(Course.category == category)
        
        if difficulty:
            stmt = stmt.where(Course.difficulty_level == difficulty)
        
        if instructor_id:
            stmt = stmt.where(Course.instructor_id == instructor_id)
        
        if status:
            stmt = stmt.where(Course.status == status)
        
        if is_public is not None:
            stmt = stmt.where(Course.is_public == is_public)
        
        stmt = stmt.offset(skip).limit(limit).order_by(Course.created_at.desc())
        courses = self.db.exec(stmt).all()
        
        # Convert to response models
        course_responses = []
        for course in courses:
            course_response = await self.get_course(course.course_id, tenant_id)
            if course_response:
                course_responses.append(course_response)
        
        return course_responses
    
    async def enroll_user(self, course_id: UUID, user_id: UUID, tenant_id: UUID, enrolled_by: UUID = None) -> bool:
        """Enroll user in course"""
        # Check if already enrolled
        existing_enrollment = self.db.exec(
            select(CourseEnrollment).where(
                CourseEnrollment.course_id == course_id,
                CourseEnrollment.user_id == user_id
            )
        ).first()
        
        if existing_enrollment:
            return False
        
        # Create enrollment
        enrollment = CourseEnrollment(
            tenant_id=tenant_id,
            user_id=user_id,
            course_id=course_id,
            enrolled_by=enrolled_by
        )
        
        self.db.add(enrollment)
        self.db.commit()
        
        # Send notification
        course = self.db.get(Course, course_id)
        user = self.db.get(User, user_id)
        
        if course and user:
            await self.notification_service.create_notification({
                "user_id": user_id,
                "title": "Course Enrollment",
                "message": f"You have been enrolled in {course.title}",
                "notification_type": "course_enrollment",
                "related_id": course_id,
                "related_type": "course"
            })
        
        return True
    
    async def add_module(self, module_data: CourseModuleCreate, tenant_id: UUID) -> CourseModule:
        """Add module to course"""
        module = CourseModule(
            tenant_id=tenant_id,
            course_id=module_data.course_id,
            title=module_data.title,
            description=module_data.description,
            order_index=module_data.order_index,
            is_published=module_data.is_published,
            estimated_duration_minutes=module_data.estimated_duration_minutes
        )
        
        self.db.add(module)
        self.db.commit()
        self.db.refresh(module)
        
        return module
    
    async def add_content_item(self, content_data: ContentItemCreate, tenant_id: UUID) -> ContentItem:
        """Add content item to module"""
        # Get module to get course_id
        module = self.db.get(CourseModule, content_data.module_id)
        if not module:
            raise ValueError("Module not found")
        
        content_item = ContentItem(
            tenant_id=tenant_id,
            module_id=content_data.module_id,
            course_id=module.course_id,
            title=content_data.title,
            content_type=content_data.content_type,
            content_data=content_data.content_data,
            order_index=content_data.order_index,
            is_required=content_data.is_required,
            estimated_duration_minutes=content_data.estimated_duration_minutes
        )
        
        self.db.add(content_item)
        self.db.commit()
        self.db.refresh(content_item)
        
        return content_item
    
    async def rate_course(self, course_id: UUID, user_id: UUID, rating_data: CourseRatingCreate, tenant_id: UUID) -> CourseRating:
        """Rate a course"""
        # Check if user already rated
        existing_rating = self.db.exec(
            select(CourseRating).where(
                CourseRating.course_id == course_id,
                CourseRating.user_id == user_id
            )
        ).first()
        
        if existing_rating:
            # Update existing rating
            existing_rating.rating = rating_data.rating
            existing_rating.review = rating_data.review
            existing_rating.updated_at = datetime.utcnow()
            
            self.db.add(existing_rating)
            self.db.commit()
            self.db.refresh(existing_rating)
            
            return existing_rating
        else:
            # Create new rating
            rating = CourseRating(
                course_id=course_id,
                user_id=user_id,
                tenant_id=tenant_id,
                rating=rating_data.rating,
                review=rating_data.review
            )
            
            self.db.add(rating)
            self.db.commit()
            self.db.refresh(rating)

            return rating

    async def get_user_recent_courses(self, user_id: UUID, tenant_id: UUID, limit: int = 5) -> List[CourseResponse]:
        """Get user's recent courses for dashboard"""
        try:
            # Get recent enrollments
            stmt = select(CourseEnrollment).where(
                CourseEnrollment.user_id == user_id,
                CourseEnrollment.tenant_id == tenant_id
            ).order_by(CourseEnrollment.last_accessed.desc()).limit(limit)

            enrollments = self.db.exec(stmt).all()

            # Get course details
            courses = []
            for enrollment in enrollments:
                course_response = await self.get_course(enrollment.course_id, tenant_id)
                if course_response:
                    courses.append(course_response)

            return courses

        except Exception as e:
            print(f"Error getting recent courses: {e}")
            return []

    async def get_recommended_courses(self, user_id: UUID, tenant_id: UUID, limit: int = 5) -> List[CourseResponse]:
        """Get recommended courses for user dashboard"""
        try:
            # Simple recommendation: get popular published courses that user hasn't enrolled in
            # Get user's enrolled course IDs
            enrolled_course_ids = self.db.exec(
                select(CourseEnrollment.course_id).where(
                    CourseEnrollment.user_id == user_id,
                    CourseEnrollment.tenant_id == tenant_id
                )
            ).all()

            # Get popular courses not enrolled in
            stmt = select(Course).where(
                Course.tenant_id == tenant_id,
                Course.status == CourseStatus.PUBLISHED,
                Course.is_public == True
            )

            if enrolled_course_ids:
                stmt = stmt.where(Course.course_id.notin_(enrolled_course_ids))

            stmt = stmt.order_by(Course.created_at.desc()).limit(limit)
            courses = self.db.exec(stmt).all()

            # Convert to response models
            course_responses = []
            for course in courses:
                course_response = await self.get_course(course.course_id, tenant_id)
                if course_response:
                    course_responses.append(course_response)

            return course_responses

        except Exception as e:
            print(f"Error getting recommended courses: {e}")
            return []

    async def get_featured_courses(self, tenant_id: UUID, limit: int = 10) -> List[CourseResponse]:
        """Get featured courses"""
        try:
            # Get published courses with high ratings or recent
            stmt = select(Course).where(
                Course.tenant_id == tenant_id,
                Course.status == CourseStatus.PUBLISHED,
                Course.is_public == True
            ).order_by(Course.created_at.desc()).limit(limit)

            courses = self.db.exec(stmt).all()

            # Convert to response models
            course_responses = []
            for course in courses:
                course_response = await self.get_course(course.course_id, tenant_id)
                if course_response:
                    course_responses.append(course_response)

            return course_responses

        except Exception as e:
            print(f"Error getting featured courses: {e}")
            return []
