"""
Configuration settings for Arroyo University Core API
"""

import os
from typing import Optional
from pydantic import field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings"""
    
    # Application
    APP_NAME: str = "Arroyo University Core API"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    ENVIRONMENT: str = "development"
    
    # API
    API_V1_STR: str = "/api/v1"
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # Security
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_HOURS: int = 24
    REFRESH_TOKEN_EXPIRE_DAYS: int = 30
    
    # Authentication
    MAX_LOGIN_ATTEMPTS: int = 5
    ACCOUNT_LOCKOUT_MINUTES: int = 30
    PASSWORD_MIN_LENGTH: int = 8

    # System Owner Configuration
    SYSTEM_OWNER_EMAIL: str = os.getenv("SYSTEM_OWNER_EMAIL", "<EMAIL>")
    SYSTEM_OWNER_PASSWORD: str = os.getenv("SYSTEM_OWNER_PASSWORD", "ChangeMe123!")
    SYSTEM_OWNER_FIRST_NAME: str = os.getenv("SYSTEM_OWNER_FIRST_NAME", "System")
    SYSTEM_OWNER_LAST_NAME: str = os.getenv("SYSTEM_OWNER_LAST_NAME", "Owner")
    
    # Database
    DATABASE_URL: str = os.getenv(
        "DATABASE_URL", 
        "postgresql://postgres:password@localhost:5432/arroyo_university"
    )
    DATABASE_POOL_SIZE: int = 10
    DATABASE_MAX_OVERFLOW: int = 20
    
    # Redis (for caching and sessions)
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    
    # Email
    SMTP_HOST: str = os.getenv("SMTP_HOST", "")
    SMTP_PORT: int = int(os.getenv("SMTP_PORT", "587"))
    SMTP_USERNAME: str = os.getenv("SMTP_USERNAME", "")
    SMTP_PASSWORD: str = os.getenv("SMTP_PASSWORD", "")
    SMTP_USE_TLS: bool = True
    FROM_EMAIL: str = os.getenv("FROM_EMAIL", "<EMAIL>")
    FROM_NAME: str = os.getenv("FROM_NAME", "Arroyo University")
    
    # File Storage
    UPLOAD_DIR: str = os.getenv("UPLOAD_DIR", "./uploads")
    MAX_FILE_SIZE_MB: int = 100
    ALLOWED_FILE_TYPES: str = "image/jpeg,image/png,image/gif,image/webp,application/pdf,text/plain,text/csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    
    # External Services
    AI_SERVICE_URL: str = os.getenv("AI_SERVICE_URL", "http://localhost:8001")
    NOTIFICATION_SERVICE_URL: str = os.getenv("NOTIFICATION_SERVICE_URL", "http://localhost:8002")
    FRONTEND_URL: str = os.getenv("FRONTEND_URL", "http://localhost:3000")
    
    # OpenAI (for AI features)
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")
    OPENAI_MODEL: str = "gpt-4-turbo-preview"
    OPENAI_MAX_TOKENS: int = 4000
    OPENAI_TEMPERATURE: float = 0.7
    OPENAI_TIMEOUT: int = 30
    OPENAI_MAX_RETRIES: int = 3

    # Azure Speech Services
    AZURE_SPEECH_KEY: str = os.getenv("AZURE_SPEECH_KEY", "")
    AZURE_SPEECH_REGION: str = os.getenv("AZURE_SPEECH_REGION", "")
    AZURE_SPEECH_ENDPOINT: str = os.getenv("AZURE_SPEECH_ENDPOINT", "")
    AZURE_TTS_VOICE: str = "en-US-AriaNeural"
    AZURE_STT_LANGUAGE: str = "en-US"

    # AI Content Moderation
    ENABLE_CONTENT_MODERATION: bool = True
    MODERATION_THRESHOLD: float = 0.8
    BLOCKED_CATEGORIES: str = "hate,harassment,self-harm,sexual,violence"

    # AI Generation Limits
    MAX_QUESTIONS_PER_REQUEST: int = 10
    MAX_GENERATION_TIME_SECONDS: int = 120
    DAILY_GENERATION_LIMIT_PER_TENANT: int = 1000
    MONTHLY_GENERATION_LIMIT_PER_TENANT: int = 10000

    # AI Scoring
    SCORING_MODEL: str = "gpt-4-turbo-preview"
    SCORING_TEMPERATURE: float = 0.1
    SCORING_MAX_TOKENS: int = 1000
    ENABLE_RUBRIC_SCORING: bool = True

    # Audio Processing
    MAX_AUDIO_DURATION_SECONDS: int = 300
    SUPPORTED_AUDIO_FORMATS: str = "mp3,wav,m4a,ogg"
    AUDIO_SAMPLE_RATE: int = 16000
    AUDIO_CHANNELS: int = 1

    # Plagiarism Detection
    ENABLE_PLAGIARISM_DETECTION: bool = True
    PLAGIARISM_THRESHOLD: float = 0.7
    TURNITIN_API_KEY: str = os.getenv("TURNITIN_API_KEY", "")
    TURNITIN_ENDPOINT: str = "https://api.turnitin.com/"

    # AI Cost Management
    ENABLE_COST_TRACKING: bool = True
    COST_ALERT_THRESHOLD_USD: float = 100.0
    DAILY_COST_LIMIT_USD: float = 500.0
    
    # Monitoring
    SENTRY_DSN: Optional[str] = os.getenv("SENTRY_DSN")
    LOG_LEVEL: str = "INFO"
    
    # CORS
    CORS_ORIGINS: str = "http://localhost:3000,http://localhost:3001,https://arroyouniversity.com"
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: str = "GET,POST,PUT,DELETE,OPTIONS"
    CORS_ALLOW_HEADERS: str = "*"
    
    # Rate Limiting
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_WINDOW: int = 60  # seconds
    
    # Pagination
    DEFAULT_PAGE_SIZE: int = 20
    MAX_PAGE_SIZE: int = 100
    
    # Features
    ENABLE_AI_FEATURES: bool = True
    ENABLE_FORUM: bool = True
    ENABLE_GROUPS: bool = True
    ENABLE_ANALYTICS: bool = True
    ENABLE_WEBHOOKS: bool = True
    
    # Multi-tenancy
    DEFAULT_TENANT_PLAN: str = "basic"
    MAX_TENANTS: int = 1000
    
    @property
    def cors_origins_list(self):
        return [i.strip() for i in self.CORS_ORIGINS.split(",")]

    @property
    def allowed_file_types_list(self):
        return [i.strip() for i in self.ALLOWED_FILE_TYPES.split(",")]

    @property
    def blocked_categories_list(self):
        return [i.strip() for i in self.BLOCKED_CATEGORIES.split(",")]

    @property
    def supported_audio_formats_list(self):
        return [i.strip() for i in self.SUPPORTED_AUDIO_FORMATS.split(",")]

    @field_validator("DATABASE_URL", mode="before")
    @classmethod
    def validate_database_url(cls, v):
        if not v:
            raise ValueError("DATABASE_URL is required")
        return v
    
    model_config = {
        "env_file": ".env",
        "case_sensitive": True,
        "extra": "ignore"
    }


# Create settings instance
settings = Settings()


# Database configuration
class DatabaseConfig:
    """Database configuration"""
    
    @staticmethod
    def get_database_url() -> str:
        """Get database URL"""
        return settings.DATABASE_URL
    
    @staticmethod
    def get_engine_config() -> dict:
        """Get SQLAlchemy engine configuration"""
        return {
            "pool_size": settings.DATABASE_POOL_SIZE,
            "max_overflow": settings.DATABASE_MAX_OVERFLOW,
            "pool_pre_ping": True,
            "pool_recycle": 3600,
        }


# Email configuration
class EmailConfig:
    """Email configuration"""
    
    @staticmethod
    def get_smtp_config() -> dict:
        """Get SMTP configuration"""
        return {
            "hostname": settings.SMTP_HOST,
            "port": settings.SMTP_PORT,
            "username": settings.SMTP_USERNAME,
            "password": settings.SMTP_PASSWORD,
            "use_tls": settings.SMTP_USE_TLS,
        }
    
    @staticmethod
    def get_from_config() -> dict:
        """Get from email configuration"""
        return {
            "email": settings.FROM_EMAIL,
            "name": settings.FROM_NAME,
        }


# Security configuration
class SecurityConfig:
    """Security configuration"""
    
    @staticmethod
    def get_password_requirements() -> dict:
        """Get password requirements"""
        return {
            "min_length": settings.PASSWORD_MIN_LENGTH,
            "require_uppercase": True,
            "require_lowercase": True,
            "require_numbers": True,
            "require_symbols": False,
        }
    
    @staticmethod
    def get_jwt_config() -> dict:
        """Get JWT configuration"""
        return {
            "secret_key": settings.SECRET_KEY,
            "algorithm": settings.ALGORITHM,
            "access_token_expire_hours": settings.ACCESS_TOKEN_EXPIRE_HOURS,
            "refresh_token_expire_days": settings.REFRESH_TOKEN_EXPIRE_DAYS,
        }


# Feature flags
class FeatureFlags:
    """Feature flags configuration"""
    
    AI_FEATURES = settings.ENABLE_AI_FEATURES
    FORUM = settings.ENABLE_FORUM
    GROUPS = settings.ENABLE_GROUPS
    ANALYTICS = settings.ENABLE_ANALYTICS
    WEBHOOKS = settings.ENABLE_WEBHOOKS
