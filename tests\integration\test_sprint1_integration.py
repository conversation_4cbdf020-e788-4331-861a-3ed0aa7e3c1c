"""
Sprint 1 Integration Tests
End-to-end integration tests that validate all Sprint 1 components working together.

These tests ensure that the infrastructure, multi-tenant architecture, and RLS
work together seamlessly to provide a complete development foundation.
"""

import pytest
import time
import requests
from sqlalchemy import text
from backend.src.models.tenant import Tenant
from backend.src.models.user import User
from backend.src.core.tenant_context import tenant_context
from backend.src.core.security import get_password_hash, create_access_token


class TestFullStackIntegration:
    """Test complete stack integration from API to database."""
    
    def test_api_to_database_flow(self, test_db, client):
        """Test complete flow from API request to database with RLS."""
        # Create tenant
        tenant = Tenant(
            name="Integration Test University",
            slug="integration-test",
            domain="integration.example.com"
        )
        test_db.add(tenant)
        test_db.commit()
        test_db.refresh(tenant)
        
        # Create user
        user = User(
            tenant_id=tenant.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="user"
        )
        test_db.add(user)
        test_db.commit()
        test_db.refresh(user)
        
        # Create JWT token
        token_data = {
            "sub": user.id,
            "tenant_id": tenant.id,
            "role": user.role,
            "email": user.email
        }
        access_token = create_access_token(data=token_data)
        
        # Test API call with authentication
        headers = {"Authorization": f"Bearer {access_token}"}
        response = client.get("/api/v1/users/me", headers=headers)
        
        # Should successfully authenticate and return user data
        assert response.status_code == 200
        data = response.json()
        assert data["email"] == "<EMAIL>"
        assert data["tenant_id"] == tenant.id
    
    def test_multi_tenant_api_isolation(self, test_db, client):
        """Test that API properly isolates tenant data."""
        # Create two tenants
        tenant1 = Tenant(name="Tenant 1", slug="tenant-1")
        tenant2 = Tenant(name="Tenant 2", slug="tenant-2")
        test_db.add_all([tenant1, tenant2])
        test_db.commit()
        test_db.refresh(tenant1)
        test_db.refresh(tenant2)
        
        # Create users for each tenant
        user1 = User(
            tenant_id=tenant1.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="admin"
        )
        
        user2 = User(
            tenant_id=tenant2.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="admin"
        )
        
        test_db.add_all([user1, user2])
        test_db.commit()
        
        # Create tokens for both users
        token1 = create_access_token(data={
            "sub": user1.id,
            "tenant_id": tenant1.id,
            "role": user1.role,
            "email": user1.email
        })
        
        token2 = create_access_token(data={
            "sub": user2.id,
            "tenant_id": tenant2.id,
            "role": user2.role,
            "email": user2.email
        })
        
        # Test that each user only sees their tenant's data
        headers1 = {"Authorization": f"Bearer {token1}"}
        headers2 = {"Authorization": f"Bearer {token2}"}
        
        response1 = client.get("/api/v1/users/", headers=headers1)
        response2 = client.get("/api/v1/users/", headers=headers2)
        
        assert response1.status_code == 200
        assert response2.status_code == 200
        
        users1 = response1.json()
        users2 = response2.json()
        
        # Each should only see their own tenant's users
        tenant1_emails = [u["email"] for u in users1]
        tenant2_emails = [u["email"] for u in users2]
        
        assert "<EMAIL>" in tenant1_emails
        assert "<EMAIL>" not in tenant1_emails
        
        assert "<EMAIL>" in tenant2_emails
        assert "<EMAIL>" not in tenant2_emails


class TestDockerEnvironmentIntegration:
    """Test Docker environment integration with application."""
    
    def test_all_services_communicate(self):
        """Test that all Docker services can communicate properly."""
        # Test backend can reach database
        try:
            response = requests.get("http://localhost:8000/health", timeout=10)
            assert response.status_code == 200
            health_data = response.json()
            assert health_data["status"] == "healthy"
        except requests.RequestException:
            pytest.fail("Backend service not accessible")
        
        # Test backend can reach Redis (if Redis health endpoint exists)
        # This would be implemented in the actual backend health check
        
        # Test database connectivity through backend
        try:
            response = requests.get("http://localhost:8000/api/v1/health/db", timeout=10)
            # This endpoint would need to be implemented to test DB connectivity
            # For now, we'll just test that backend is responding
            assert response.status_code in [200, 404]  # 404 if endpoint not implemented yet
        except requests.RequestException:
            pytest.fail("Cannot test database connectivity through backend")
    
    def test_environment_variables_loaded(self, client):
        """Test that environment variables are properly loaded in containers."""
        # Test that the application has loaded environment configuration
        response = client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        assert "version" in data
        assert data["message"] == "ArroyoUniversity API"
    
    def test_docker_volumes_working(self, test_db):
        """Test that Docker volumes are properly mounted and persistent."""
        # Create test data
        tenant = Tenant(
            name="Volume Test University",
            slug="volume-test"
        )
        test_db.add(tenant)
        test_db.commit()
        
        # Data should persist (this is a basic test)
        test_db.refresh(tenant)
        assert tenant.name == "Volume Test University"


class TestPerformanceIntegration:
    """Test performance characteristics of the integrated system."""
    
    def test_api_response_times_acceptable(self, client, auth_headers):
        """Test that API response times are acceptable under normal load."""
        endpoints_to_test = [
            "/",
            "/health",
            "/api/v1/users/me"
        ]
        
        for endpoint in endpoints_to_test:
            start_time = time.time()
            
            if endpoint.startswith("/api/v1/"):
                response = client.get(endpoint, headers=auth_headers)
            else:
                response = client.get(endpoint)
            
            end_time = time.time()
            response_time = end_time - start_time
            
            assert response.status_code in [200, 401]  # 401 for protected endpoints without auth
            assert response_time < 1.0, f"{endpoint} took {response_time:.2f}s, should be under 1s"
    
    def test_database_performance_with_rls(self, test_db, sample_tenant):
        """Test that database performance is acceptable with RLS enabled."""
        # Create test data
        users = []
        for i in range(50):
            user = User(
                tenant_id=sample_tenant.id,
                email=f"perf{i}@tenant.com",
                hashed_password=get_password_hash("password123"),
                role="user"
            )
            users.append(user)
        
        test_db.add_all(users)
        test_db.commit()
        
        # Test query performance with RLS
        with tenant_context(test_db, sample_tenant.id):
            start_time = time.time()
            
            # Perform multiple queries
            for _ in range(10):
                result = test_db.query(User).filter(
                    User.email.like("perf%")
                ).limit(10).all()
                assert len(result) == 10
            
            end_time = time.time()
            total_time = end_time - start_time
            
            assert total_time < 1.0, f"Database queries took {total_time:.2f}s, should be under 1s"
    
    def test_concurrent_tenant_access(self, test_db, sample_tenant, sample_tenant_2):
        """Test performance with concurrent access from multiple tenants."""
        import threading
        import queue
        
        # Create users for both tenants
        user1 = User(
            tenant_id=sample_tenant.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="user"
        )
        
        user2 = User(
            tenant_id=sample_tenant_2.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="user"
        )
        
        test_db.add_all([user1, user2])
        test_db.commit()
        
        results = queue.Queue()
        
        def query_tenant(tenant_id, result_queue):
            """Query function to run in separate thread."""
            try:
                with tenant_context(test_db, tenant_id):
                    start_time = time.time()
                    users = test_db.query(User).all()
                    end_time = time.time()
                    
                    result_queue.put({
                        'tenant_id': tenant_id,
                        'user_count': len(users),
                        'query_time': end_time - start_time,
                        'success': True
                    })
            except Exception as e:
                result_queue.put({
                    'tenant_id': tenant_id,
                    'error': str(e),
                    'success': False
                })
        
        # Start concurrent queries
        thread1 = threading.Thread(target=query_tenant, args=(sample_tenant.id, results))
        thread2 = threading.Thread(target=query_tenant, args=(sample_tenant_2.id, results))
        
        thread1.start()
        thread2.start()
        
        thread1.join(timeout=5)
        thread2.join(timeout=5)
        
        # Check results
        result_list = []
        while not results.empty():
            result_list.append(results.get())
        
        assert len(result_list) == 2
        
        for result in result_list:
            assert result['success'] is True
            assert result['query_time'] < 1.0
            assert result['user_count'] >= 1


class TestErrorHandlingIntegration:
    """Test error handling across the integrated system."""
    
    def test_database_error_recovery(self, test_db, client):
        """Test that the system recovers gracefully from database errors."""
        # Test invalid SQL doesn't crash the application
        try:
            test_db.execute(text("SELECT * FROM nonexistent_table"))
        except Exception:
            pass  # Expected to fail
        
        # Application should still be responsive
        response = client.get("/health")
        assert response.status_code == 200
        
        # Database session should still work
        result = test_db.execute(text("SELECT 1")).scalar()
        assert result == 1
    
    def test_invalid_tenant_context_handling(self, test_db, client):
        """Test handling of invalid tenant contexts."""
        import uuid
        
        # Set invalid tenant context
        invalid_tenant_id = str(uuid.uuid4())
        
        with tenant_context(test_db, invalid_tenant_id):
            # Should not crash, should return empty results
            users = test_db.query(User).all()
            assert isinstance(users, list)
            assert len(users) == 0
        
        # Application should still be responsive
        response = client.get("/health")
        assert response.status_code == 200
    
    def test_authentication_error_handling(self, client):
        """Test that authentication errors are handled properly."""
        # Test with invalid token
        headers = {"Authorization": "Bearer invalid-token"}
        response = client.get("/api/v1/users/me", headers=headers)
        
        assert response.status_code == 401
        assert "detail" in response.json()
        
        # Test with no token
        response = client.get("/api/v1/users/me")
        assert response.status_code == 401


class TestSecurityIntegration:
    """Test security features working together across the system."""
    
    def test_end_to_end_tenant_isolation(self, test_db, client):
        """Test complete tenant isolation from API to database."""
        # Create two tenants with sensitive data
        tenant1 = Tenant(name="Secure Tenant 1", slug="secure-1")
        tenant2 = Tenant(name="Secure Tenant 2", slug="secure-2")
        test_db.add_all([tenant1, tenant2])
        test_db.commit()
        test_db.refresh(tenant1)
        test_db.refresh(tenant2)
        
        # Create admin users for each tenant
        admin1 = User(
            tenant_id=tenant1.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("secret123"),
            role="admin",
            first_name="Confidential",
            last_name="Data1"
        )
        
        admin2 = User(
            tenant_id=tenant2.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("secret456"),
            role="admin",
            first_name="Confidential",
            last_name="Data2"
        )
        
        test_db.add_all([admin1, admin2])
        test_db.commit()
        
        # Create tokens for both admins
        token1 = create_access_token(data={
            "sub": admin1.id,
            "tenant_id": tenant1.id,
            "role": admin1.role,
            "email": admin1.email
        })
        
        token2 = create_access_token(data={
            "sub": admin2.id,
            "tenant_id": tenant2.id,
            "role": admin2.role,
            "email": admin2.email
        })
        
        # Test that admin1 cannot see admin2's data through API
        headers1 = {"Authorization": f"Bearer {token1}"}
        response1 = client.get("/api/v1/users/", headers=headers1)
        
        assert response1.status_code == 200
        users1 = response1.json()
        
        # Should only see users from tenant 1
        emails1 = [u["email"] for u in users1]
        assert "<EMAIL>" in emails1
        assert "<EMAIL>" not in emails1
        
        # Test that admin2 cannot see admin1's data through API
        headers2 = {"Authorization": f"Bearer {token2}"}
        response2 = client.get("/api/v1/users/", headers=headers2)
        
        assert response2.status_code == 200
        users2 = response2.json()
        
        # Should only see users from tenant 2
        emails2 = [u["email"] for u in users2]
        assert "<EMAIL>" in emails2
        assert "<EMAIL>" not in emails2
    
    def test_jwt_token_tenant_binding(self, test_db, client):
        """Test that JWT tokens are properly bound to tenants."""
        # Create tenant and user
        tenant = Tenant(name="Token Test Tenant", slug="token-test")
        test_db.add(tenant)
        test_db.commit()
        test_db.refresh(tenant)
        
        user = User(
            tenant_id=tenant.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="user"
        )
        test_db.add(user)
        test_db.commit()
        test_db.refresh(user)
        
        # Create token with correct tenant
        valid_token = create_access_token(data={
            "sub": user.id,
            "tenant_id": tenant.id,
            "role": user.role,
            "email": user.email
        })
        
        # Create token with wrong tenant
        wrong_tenant_id = str(uuid.uuid4())
        invalid_token = create_access_token(data={
            "sub": user.id,
            "tenant_id": wrong_tenant_id,
            "role": user.role,
            "email": user.email
        })
        
        # Test with valid token
        headers_valid = {"Authorization": f"Bearer {valid_token}"}
        response_valid = client.get("/api/v1/users/me", headers=headers_valid)
        assert response_valid.status_code == 200
        
        # Test with invalid tenant in token
        headers_invalid = {"Authorization": f"Bearer {invalid_token}"}
        response_invalid = client.get("/api/v1/users/me", headers=headers_invalid)
        # Should fail because user doesn't exist in the wrong tenant context
        assert response_invalid.status_code in [401, 404]


class TestAcceptanceCriteriaIntegration:
    """Test that all Sprint 1 acceptance criteria work together."""
    
    def test_complete_sprint1_user_journey(self, test_db, client):
        """
        Test complete user journey validating all Sprint 1 acceptance criteria:
        1. Environment setup works
        2. Multi-tenant isolation works
        3. Database security works
        """
        # Step 1: Verify environment is working (infrastructure)
        response = client.get("/health")
        assert response.status_code == 200
        assert response.json()["status"] == "healthy"
        
        # Step 2: Create multi-tenant setup
        tenant = Tenant(
            name="Journey Test University",
            slug="journey-test",
            settings={"theme": "blue", "max_users": 100}
        )
        test_db.add(tenant)
        test_db.commit()
        test_db.refresh(tenant)
        
        # Step 3: Create user with proper tenant isolation
        user = User(
            tenant_id=tenant.id,
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            role="user"
        )
        test_db.add(user)
        test_db.commit()
        test_db.refresh(user)
        
        # Step 4: Test authentication and API access
        token = create_access_token(data={
            "sub": user.id,
            "tenant_id": tenant.id,
            "role": user.role,
            "email": user.email
        })
        
        headers = {"Authorization": f"Bearer {token}"}
        response = client.get("/api/v1/users/me", headers=headers)
        assert response.status_code == 200
        
        user_data = response.json()
        assert user_data["email"] == "<EMAIL>"
        assert user_data["tenant_id"] == tenant.id
        
        # Step 5: Verify database-level security (RLS)
        with tenant_context(test_db, tenant.id):
            # Should see the user in correct tenant context
            found_user = test_db.query(User).filter(
                User.email == "<EMAIL>"
            ).first()
            assert found_user is not None
            assert found_user.tenant_id == tenant.id
        
        # Step 6: Verify isolation from other tenants
        other_tenant_id = str(uuid.uuid4())
        with tenant_context(test_db, other_tenant_id):
            # Should not see the user in different tenant context
            not_found_user = test_db.query(User).filter(
                User.email == "<EMAIL>"
            ).first()
            assert not_found_user is None
        
        # If we reach here, all Sprint 1 components are working together
        assert True, "Complete Sprint 1 user journey successful"
