import React from 'react';
import { <PERSON><PERSON><PERSON>cle, BookOpen, Star, Zap, Users, TrendingUp } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/Card';
import { useAuthStore } from '@/store/authStore';
import { useQuery } from '@tanstack/react-query';
import { courseService } from '@/services/courseService';

// Empty state components
const EmptyState = ({ icon: Icon, title, description }: { icon: any, title: string, description: string }) => (
  <div className="text-center py-8">
    <Icon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
    <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
    <p className="text-gray-500">{description}</p>
  </div>
);

export default function HomePage() {
  const { user } = useAuthStore();

  // Fetch user dashboard data
  const { data: userStats, isLoading: statsLoading } = useQuery({
    queryKey: ['user-stats', user?.user_id],
    queryFn: () => courseService.getUserStats(),
    enabled: !!user,
  });

  const { data: recentCourses, isLoading: recentLoading } = useQuery({
    queryKey: ['recent-courses', user?.user_id],
    queryFn: () => courseService.getRecentCourses(),
    enabled: !!user,
  });

  const { data: recommendedCourses, isLoading: recommendedLoading } = useQuery({
    queryKey: ['recommended-courses', user?.user_id],
    queryFn: () => courseService.getRecommendedCourses(),
    enabled: !!user,
  });

  // Default stats structure
  const defaultStats = [
    {
      name: 'Cursos Completados',
      value: userStats?.completedCourses || 0,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      name: 'En Progreso',
      value: userStats?.inProgressCourses || 0,
      icon: BookOpen,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      name: 'Guardados',
      value: userStats?.savedCourses || 0,
      icon: Star,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
    },
    {
      name: 'Racha de Días',
      value: userStats?.streakDays || 0,
      icon: Zap,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Welcome Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          ¡Bienvenido de vuelta, {user?.first_name || 'Usuario'}!
        </h1>
        <p className="text-gray-600 mt-2">
          {user ? 'Continúa tu progreso de aprendizaje' : 'Inicia sesión para ver tu progreso'}
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {defaultStats.map((stat) => {
          const Icon = stat.icon;
          return (
            <Card key={stat.name}>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className={`flex-shrink-0 p-3 rounded-lg ${stat.bgColor}`}>
                    <Icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500 truncate">
                      {stat.name}
                    </p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {statsLoading ? '...' : stat.value}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Courses */}
        <Card>
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">
              Cursos Recientes
            </h3>
          </div>
          <CardContent className="p-6">
            {recentLoading ? (
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
                    <div className="h-2 bg-gray-200 rounded w-full"></div>
                  </div>
                ))}
              </div>
            ) : recentCourses && recentCourses.length > 0 ? (
              <div className="space-y-4">
                {recentCourses.map((course: any) => (
                  <div key={course.id} className="flex items-center justify-between">
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900">
                        {course.title}
                      </h4>
                      <p className="text-xs text-gray-500">
                        {course.category} • Último acceso: {course.lastAccessed}
                      </p>
                      <div className="mt-2">
                        <div className="flex items-center">
                          <div className="flex-1 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full"
                              style={{ width: `${course.progress || 0}%` }}
                            />
                          </div>
                          <span className="ml-2 text-xs text-gray-500">
                            {course.progress || 0}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <EmptyState
                icon={BookOpen}
                title="No hay cursos recientes"
                description="Comienza explorando el marketplace para encontrar cursos interesantes."
              />
            )}
          </CardContent>
        </Card>

        {/* Recommended Courses */}
        <Card>
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">
              Cursos Recomendados
            </h3>
          </div>
          <CardContent className="p-6">
            {recommendedLoading ? (
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                  </div>
                ))}
              </div>
            ) : recommendedCourses && recommendedCourses.length > 0 ? (
              <div className="space-y-4">
                {recommendedCourses.map((course: any) => (
                  <div key={course.id} className="flex items-center justify-between">
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900">
                        {course.title}
                      </h4>
                      <p className="text-xs text-gray-500 mb-2">
                        {course.category}
                      </p>
                      <div className="flex items-center text-xs text-gray-500">
                        <Star className="w-3 h-3 mr-1 fill-current text-yellow-400" />
                        {course.rating || 0}
                        <span className="mx-2">•</span>
                        <span>{course.students || 0} estudiantes</span>
                      </div>
                    </div>
                    <button className="ml-4 px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700">
                      Ver
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <EmptyState
                icon={TrendingUp}
                title="No hay recomendaciones"
                description="Las recomendaciones aparecerán basadas en tu actividad y preferencias."
              />
            )}
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="mt-8">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Acciones Rápidas
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <BookOpen className="w-6 h-6 text-blue-600" />
              </div>
              <h4 className="text-sm font-medium text-gray-900 mb-1">
                Explorar Cursos
              </h4>
              <p className="text-xs text-gray-500">
                Descubre nuevos cursos en el marketplace
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Zap className="w-6 h-6 text-green-600" />
              </div>
              <h4 className="text-sm font-medium text-gray-900 mb-1">
                Career Paths
              </h4>
              <p className="text-xs text-gray-500">
                Planifica tu carrera profesional
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Star className="w-6 h-6 text-purple-600" />
              </div>
              <h4 className="text-sm font-medium text-gray-900 mb-1">
                Leaderboard
              </h4>
              <p className="text-xs text-gray-500">
                Ve tu posición en el ranking
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
