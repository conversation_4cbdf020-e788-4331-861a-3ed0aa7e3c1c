# Sprint 2: User and Tenant Management Implementation Guide

## Overview
This sprint implements JWT authentication, CI/CD pipeline, tenant CRUD operations, user management, admin interface, and invitation system.

## Timeline: 2 weeks

## Prerequisites
- Sprint 1 completed successfully
- Docker environment running
- Database with RLS configured

## Step 1: JWT Authentication Implementation

### 1.1 Create Authentication Models
Create `backend/src/models/user.py`:
```python
from sqlalchemy import Column, String, Boolean, DateTime, Integer
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base, TimestampMixin, UUIDMixin, SoftDeleteMixin, TenantMixin

class User(Base, UUIDMixin, TimestampMixin, SoftDeleteMixin, TenantMixin):
    __tablename__ = "users"
    
    # Basic information
    email = Column(String(255), nullable=False, index=True)
    username = Column(String(100), nullable=True, index=True)
    first_name = Column(String(100), nullable=True)
    last_name = Column(String(100), nullable=True)
    
    # Authentication
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    
    # Role and permissions
    role = Column(String(50), default="user", nullable=False)  # owner, admin, user
    
    # Login tracking
    last_login = Column(DateTime(timezone=True), nullable=True)
    login_count = Column(Integer, default=0)
    
    # Profile
    avatar_url = Column(String(500), nullable=True)
    bio = Column(String(500), nullable=True)
    timezone = Column(String(50), default="UTC")
    language = Column(String(10), default="en")
    
    # Relationships
    tenant = relationship("Tenant", back_populates="users")
    
    @property
    def full_name(self):
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.first_name or self.last_name or self.username or self.email
    
    @property
    def is_owner(self):
        return self.role == "owner"
    
    @property
    def is_admin(self):
        return self.role in ["owner", "admin"]
    
    def __repr__(self):
        return f"<User(id={self.id}, email={self.email}, role={self.role})>"
```

### 1.2 Create Authentication Utilities
Create `backend/src/core/security.py`:
```python
from datetime import datetime, timedelta
from typing import Optional, Union
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status
import secrets

from .config import settings

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Hash a password"""
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create a JWT access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> dict:
    """Verify and decode a JWT token"""
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        return payload
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

def generate_reset_token() -> str:
    """Generate a secure reset token"""
    return secrets.token_urlsafe(32)

def generate_verification_token() -> str:
    """Generate a secure email verification token"""
    return secrets.token_urlsafe(32)
```

### 1.3 Create Authentication Dependencies
Create `backend/src/core/deps.py`:
```python
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from jose import JWTError

from .database import get_db
from .security import verify_token
from .tenant_context import tenant_context
from ..models.user import User
from ..models.tenant import Tenant

security = HTTPBearer()

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """Get current authenticated user"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = verify_token(credentials.credentials)
        user_id: str = payload.get("sub")
        tenant_id: str = payload.get("tenant_id")
        
        if user_id is None or tenant_id is None:
            raise credentials_exception
            
    except JWTError:
        raise credentials_exception
    
    # Set tenant context for RLS
    with tenant_context(db, tenant_id, user_id):
        user = db.query(User).filter(User.id == user_id).first()
        if user is None:
            raise credentials_exception
        return user

async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """Get current active user"""
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

async def get_current_admin_user(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """Get current admin user"""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return current_user

async def get_current_tenant(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Tenant:
    """Get current user's tenant"""
    tenant = db.query(Tenant).filter(Tenant.id == current_user.tenant_id).first()
    if not tenant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tenant not found"
        )
    return tenant
```

### 1.4 Create Authentication Schemas
Create `backend/src/schemas/auth.py`:
```python
from pydantic import BaseModel, EmailStr, validator
from typing import Optional
from datetime import datetime

class UserBase(BaseModel):
    email: EmailStr
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    is_active: bool = True
    role: str = "user"

class UserCreate(UserBase):
    password: str
    tenant_id: str
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        return v

class UserUpdate(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    username: Optional[str] = None
    is_active: Optional[bool] = None
    role: Optional[str] = None

class UserInDB(UserBase):
    id: str
    tenant_id: str
    is_verified: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: UserInDB

class TokenData(BaseModel):
    user_id: Optional[str] = None
    tenant_id: Optional[str] = None

class LoginRequest(BaseModel):
    email: EmailStr
    password: str
    tenant_slug: Optional[str] = None

class PasswordResetRequest(BaseModel):
    email: EmailStr
    tenant_slug: str

class PasswordReset(BaseModel):
    token: str
    new_password: str

    @validator('new_password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        return v
```

## Step 2: Authentication API Endpoints

### 2.1 Create Authentication Service
Create `backend/src/services/auth_service.py`:
```python
from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from datetime import datetime, timedelta
from typing import Optional

from ..models.user import User
from ..models.tenant import Tenant
from ..schemas.auth import UserCreate, LoginRequest
from ..core.security import verify_password, get_password_hash, create_access_token
from ..core.tenant_context import tenant_context
from ..core.config import settings

class AuthService:
    def __init__(self, db: Session):
        self.db = db

    def authenticate_user(self, login_data: LoginRequest) -> Optional[User]:
        """Authenticate user with email and password"""
        # First, find the tenant
        tenant = None
        if login_data.tenant_slug:
            tenant = self.db.query(Tenant).filter(
                Tenant.slug == login_data.tenant_slug,
                Tenant.is_active == True
            ).first()
            if not tenant:
                return None

        # Find user by email
        if tenant:
            with tenant_context(self.db, tenant.id):
                user = self.db.query(User).filter(
                    User.email == login_data.email,
                    User.is_active == True
                ).first()
        else:
            # For owner login without tenant slug
            user = self.db.query(User).filter(
                User.email == login_data.email,
                User.role == "owner",
                User.is_active == True
            ).first()

        if not user or not verify_password(login_data.password, user.hashed_password):
            return None

        # Update login tracking
        user.last_login = datetime.utcnow()
        user.login_count += 1
        self.db.commit()

        return user

    def create_access_token_for_user(self, user: User) -> dict:
        """Create access token for authenticated user"""
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

        token_data = {
            "sub": user.id,
            "tenant_id": user.tenant_id,
            "role": user.role,
            "email": user.email
        }

        access_token = create_access_token(
            data=token_data,
            expires_delta=access_token_expires
        )

        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            "user": user
        }

    def create_user(self, user_data: UserCreate) -> User:
        """Create a new user"""
        # Check if user already exists
        with tenant_context(self.db, user_data.tenant_id):
            existing_user = self.db.query(User).filter(
                User.email == user_data.email
            ).first()

            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already registered"
                )

            # Create new user
            hashed_password = get_password_hash(user_data.password)
            db_user = User(
                email=user_data.email,
                username=user_data.username,
                first_name=user_data.first_name,
                last_name=user_data.last_name,
                hashed_password=hashed_password,
                role=user_data.role,
                tenant_id=user_data.tenant_id
            )

            self.db.add(db_user)
            self.db.commit()
            self.db.refresh(db_user)

            return db_user
```

### 2.2 Create Authentication Router
Create `backend/src/api/v1/endpoints/auth.py`:
```python
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from ....core.database import get_db
from ....schemas.auth import Token, LoginRequest, UserCreate, UserInDB
from ....services.auth_service import AuthService

router = APIRouter()

@router.post("/login", response_model=Token)
async def login(
    login_data: LoginRequest,
    db: Session = Depends(get_db)
):
    """Authenticate user and return access token"""
    auth_service = AuthService(db)
    user = auth_service.authenticate_user(login_data)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return auth_service.create_access_token_for_user(user)

@router.post("/register", response_model=UserInDB)
async def register(
    user_data: UserCreate,
    db: Session = Depends(get_db)
):
    """Register a new user"""
    auth_service = AuthService(db)
    user = auth_service.create_user(user_data)
    return user

@router.post("/token", response_model=Token)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """OAuth2 compatible token endpoint"""
    login_data = LoginRequest(
        email=form_data.username,  # OAuth2 uses username field for email
        password=form_data.password
    )

    auth_service = AuthService(db)
    user = auth_service.authenticate_user(login_data)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return auth_service.create_access_token_for_user(user)
```

## Step 3: Tenant Management Implementation

### 3.1 Create Tenant Schemas
Create `backend/src/schemas/tenant.py`:
```python
from pydantic import BaseModel, validator
from typing import Optional, Dict, Any
from datetime import datetime

class TenantBase(BaseModel):
    name: str
    slug: str
    domain: Optional[str] = None
    is_active: bool = True
    contact_email: Optional[str] = None
    contact_phone: Optional[str] = None
    address: Optional[str] = None

class TenantCreate(TenantBase):
    # Owner user information
    owner_email: str
    owner_password: str
    owner_first_name: Optional[str] = None
    owner_last_name: Optional[str] = None

    @validator('slug')
    def validate_slug(cls, v):
        if not v.replace('-', '').replace('_', '').isalnum():
            raise ValueError('Slug must contain only alphanumeric characters, hyphens, and underscores')
        return v.lower()

class TenantUpdate(BaseModel):
    name: Optional[str] = None
    domain: Optional[str] = None
    is_active: Optional[bool] = None
    settings: Optional[Dict[str, Any]] = None
    logo_url: Optional[str] = None
    primary_color: Optional[str] = None
    secondary_color: Optional[str] = None
    contact_email: Optional[str] = None
    contact_phone: Optional[str] = None
    address: Optional[str] = None

class TenantInDB(TenantBase):
    id: str
    settings: Dict[str, Any]
    logo_url: Optional[str]
    primary_color: str
    secondary_color: str
    plan: str
    max_users: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class TenantWithStats(TenantInDB):
    user_count: int
    active_user_count: int
```

### 3.2 Create Tenant Service
Create `backend/src/services/tenant_service.py`:
```python
from sqlalchemy.orm import Session
from sqlalchemy import func
from fastapi import HTTPException, status
from typing import List, Optional

from ..models.tenant import Tenant
from ..models.user import User
from ..schemas.tenant import TenantCreate, TenantUpdate, TenantWithStats
from ..services.auth_service import AuthService
from ..schemas.auth import UserCreate
from ..core.tenant_context import tenant_context

class TenantService:
    def __init__(self, db: Session):
        self.db = db

    def create_tenant(self, tenant_data: TenantCreate) -> Tenant:
        """Create a new tenant with owner user"""
        # Check if slug already exists
        existing_tenant = self.db.query(Tenant).filter(
            Tenant.slug == tenant_data.slug
        ).first()

        if existing_tenant:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Tenant slug already exists"
            )

        # Check if domain already exists (if provided)
        if tenant_data.domain:
            existing_domain = self.db.query(Tenant).filter(
                Tenant.domain == tenant_data.domain
            ).first()

            if existing_domain:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Domain already exists"
                )

        # Create tenant
        db_tenant = Tenant(
            name=tenant_data.name,
            slug=tenant_data.slug,
            domain=tenant_data.domain,
            contact_email=tenant_data.contact_email,
            contact_phone=tenant_data.contact_phone,
            address=tenant_data.address
        )

        self.db.add(db_tenant)
        self.db.flush()  # Get the ID without committing

        # Create owner user
        auth_service = AuthService(self.db)
        owner_data = UserCreate(
            email=tenant_data.owner_email,
            password=tenant_data.owner_password,
            first_name=tenant_data.owner_first_name,
            last_name=tenant_data.owner_last_name,
            role="owner",
            tenant_id=db_tenant.id
        )

        auth_service.create_user(owner_data)

        self.db.commit()
        self.db.refresh(db_tenant)

        return db_tenant

    def get_tenant_by_id(self, tenant_id: str) -> Optional[Tenant]:
        """Get tenant by ID"""
        return self.db.query(Tenant).filter(
            Tenant.id == tenant_id,
            Tenant.is_deleted == False
        ).first()

    def get_tenant_by_slug(self, slug: str) -> Optional[Tenant]:
        """Get tenant by slug"""
        return self.db.query(Tenant).filter(
            Tenant.slug == slug,
            Tenant.is_deleted == False
        ).first()

    def get_tenants_with_stats(self, skip: int = 0, limit: int = 100) -> List[TenantWithStats]:
        """Get tenants with user statistics"""
        tenants = self.db.query(
            Tenant,
            func.count(User.id).label('user_count'),
            func.count(func.nullif(User.is_active, False)).label('active_user_count')
        ).outerjoin(User).filter(
            Tenant.is_deleted == False
        ).group_by(Tenant.id).offset(skip).limit(limit).all()

        result = []
        for tenant, user_count, active_user_count in tenants:
            tenant_dict = tenant.__dict__.copy()
            tenant_dict['user_count'] = user_count or 0
            tenant_dict['active_user_count'] = active_user_count or 0
            result.append(TenantWithStats(**tenant_dict))

        return result

    def update_tenant(self, tenant_id: str, tenant_data: TenantUpdate) -> Tenant:
        """Update tenant information"""
        tenant = self.get_tenant_by_id(tenant_id)
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        update_data = tenant_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(tenant, field, value)

        self.db.commit()
        self.db.refresh(tenant)

        return tenant

    def delete_tenant(self, tenant_id: str) -> bool:
        """Soft delete tenant"""
        tenant = self.get_tenant_by_id(tenant_id)
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        tenant.soft_delete()
        self.db.commit()

        return True
```

## Step 4: CI/CD Pipeline Implementation

### 4.1 Create GitHub Actions Workflow
Create `.github/workflows/ci-cd.yml`:
```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '18'

jobs:
  test-backend:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('backend/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install dependencies
      run: |
        cd backend
        pip install --upgrade pip
        pip install -r requirements.txt

    - name: Run tests
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379
        SECRET_KEY: test-secret-key
      run: |
        cd backend
        pytest -v --cov=src --cov-report=xml

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage.xml
        flags: backend
        name: backend-coverage

  test-frontend:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: Install dependencies
      run: |
        cd frontend
        npm ci

    - name: Run tests
      run: |
        cd frontend
        npm test -- --coverage --watchAll=false

    - name: Build application
      run: |
        cd frontend
        npm run build

  security-scan:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  deploy-staging:
    needs: [test-backend, test-frontend, security-scan]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'

    steps:
    - uses: actions/checkout@v4

    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add your staging deployment commands here

    - name: Run health checks
      run: |
        echo "Running health checks..."
        # Add health check commands here

  deploy-production:
    needs: [test-backend, test-frontend, security-scan]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v4

    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # Add your production deployment commands here

    - name: Run health checks
      run: |
        echo "Running production health checks..."
        # Add health check commands here
```

## Step 5: Admin Interface Implementation

### 5.1 Create User Management Endpoints
Create `backend/src/api/v1/endpoints/users.py`:
```python
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List

from ....core.database import get_db
from ....core.deps import get_current_admin_user, get_current_tenant
from ....models.user import User
from ....models.tenant import Tenant
from ....schemas.auth import UserInDB, UserCreate, UserUpdate
from ....services.user_service import UserService
from ....core.tenant_context import tenant_context

router = APIRouter()

@router.get("/", response_model=List[UserInDB])
async def get_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Get all users in the current tenant"""
    with tenant_context(db, current_tenant.id):
        users = db.query(User).filter(
            User.is_deleted == False
        ).offset(skip).limit(limit).all()

    return users

@router.post("/", response_model=UserInDB)
async def create_user(
    user_data: UserCreate,
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Create a new user in the current tenant"""
    user_data.tenant_id = current_tenant.id
    user_service = UserService(db)
    return user_service.create_user(user_data)

@router.get("/{user_id}", response_model=UserInDB)
async def get_user(
    user_id: str,
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Get a specific user by ID"""
    with tenant_context(db, current_tenant.id):
        user = db.query(User).filter(
            User.id == user_id,
            User.is_deleted == False
        ).first()

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    return user

@router.put("/{user_id}", response_model=UserInDB)
async def update_user(
    user_id: str,
    user_data: UserUpdate,
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Update a user"""
    user_service = UserService(db)
    return user_service.update_user(user_id, user_data, current_tenant.id)

@router.delete("/{user_id}")
async def delete_user(
    user_id: str,
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Soft delete a user"""
    if user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete yourself"
        )

    user_service = UserService(db)
    user_service.delete_user(user_id, current_tenant.id)

    return {"message": "User deleted successfully"}
```

### 5.2 Create Tenant Admin Endpoints
Create `backend/src/api/v1/endpoints/tenants.py`:
```python
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List

from ....core.database import get_db
from ....core.deps import get_current_admin_user, get_current_tenant
from ....models.user import User
from ....models.tenant import Tenant
from ....schemas.tenant import TenantInDB, TenantUpdate, TenantWithStats
from ....services.tenant_service import TenantService

router = APIRouter()

@router.get("/current", response_model=TenantInDB)
async def get_current_tenant_info(
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """Get current tenant information"""
    return current_tenant

@router.put("/current", response_model=TenantInDB)
async def update_current_tenant(
    tenant_data: TenantUpdate,
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Update current tenant information"""
    tenant_service = TenantService(db)
    return tenant_service.update_tenant(current_tenant.id, tenant_data)

# System owner only endpoints
@router.get("/", response_model=List[TenantWithStats])
async def get_all_tenants(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Get all tenants (system owner only)"""
    if current_user.role != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only system owners can access this endpoint"
        )

    tenant_service = TenantService(db)
    return tenant_service.get_tenants_with_stats(skip, limit)

@router.get("/{tenant_id}", response_model=TenantInDB)
async def get_tenant(
    tenant_id: str,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Get specific tenant (system owner only)"""
    if current_user.role != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only system owners can access this endpoint"
        )

    tenant_service = TenantService(db)
    tenant = tenant_service.get_tenant_by_id(tenant_id)

    if not tenant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tenant not found"
        )

    return tenant
```

## Step 6: Invitation System Implementation

### 6.1 Create Invitation Models
Create `backend/src/models/invitation.py`:
```python
from sqlalchemy import Column, String, Boolean, DateTime, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime, timedelta
from .base import Base, TimestampMixin, UUIDMixin, TenantMixin

class Invitation(Base, UUIDMixin, TimestampMixin, TenantMixin):
    __tablename__ = "invitations"

    # Invitation details
    email = Column(String(255), nullable=False, index=True)
    token = Column(String(255), unique=True, nullable=False, index=True)
    role = Column(String(50), default="user", nullable=False)

    # Invitation status
    is_used = Column(Boolean, default=False, nullable=False)
    used_at = Column(DateTime(timezone=True), nullable=True)
    expires_at = Column(DateTime(timezone=True), nullable=False)

    # Inviter information
    invited_by = Column(String, nullable=False)  # User ID who sent invitation

    # Optional user information
    first_name = Column(String(100), nullable=True)
    last_name = Column(String(100), nullable=True)
    message = Column(Text, nullable=True)

    # Relationships
    tenant = relationship("Tenant")

    @property
    def is_expired(self):
        return datetime.utcnow() > self.expires_at

    @property
    def is_valid(self):
        return not self.is_used and not self.is_expired

    def __repr__(self):
        return f"<Invitation(id={self.id}, email={self.email}, is_used={self.is_used})>"
```

### 6.2 Create Invitation Schemas
Create `backend/src/schemas/invitation.py`:
```python
from pydantic import BaseModel, EmailStr, validator
from typing import Optional
from datetime import datetime

class InvitationBase(BaseModel):
    email: EmailStr
    role: str = "user"
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    message: Optional[str] = None

class InvitationCreate(InvitationBase):
    pass

class InvitationAccept(BaseModel):
    token: str
    password: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None

    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        return v

class InvitationInDB(InvitationBase):
    id: str
    tenant_id: str
    token: str
    is_used: bool
    expires_at: datetime
    invited_by: str
    created_at: datetime

    class Config:
        from_attributes = True
```

### 6.3 Create Invitation Service
Create `backend/src/services/invitation_service.py`:
```python
from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from datetime import datetime, timedelta
from typing import List, Optional
import secrets

from ..models.invitation import Invitation
from ..models.user import User
from ..schemas.invitation import InvitationCreate, InvitationAccept
from ..schemas.auth import UserCreate
from ..services.auth_service import AuthService
from ..core.tenant_context import tenant_context

class InvitationService:
    def __init__(self, db: Session):
        self.db = db

    def create_invitation(
        self,
        invitation_data: InvitationCreate,
        tenant_id: str,
        invited_by: str
    ) -> Invitation:
        """Create a new invitation"""
        with tenant_context(self.db, tenant_id):
            # Check if user already exists
            existing_user = self.db.query(User).filter(
                User.email == invitation_data.email
            ).first()

            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="User with this email already exists"
                )

            # Check if there's already a pending invitation
            existing_invitation = self.db.query(Invitation).filter(
                Invitation.email == invitation_data.email,
                Invitation.is_used == False,
                Invitation.expires_at > datetime.utcnow()
            ).first()

            if existing_invitation:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Pending invitation already exists for this email"
                )

            # Create invitation
            invitation = Invitation(
                email=invitation_data.email,
                role=invitation_data.role,
                first_name=invitation_data.first_name,
                last_name=invitation_data.last_name,
                message=invitation_data.message,
                token=secrets.token_urlsafe(32),
                tenant_id=tenant_id,
                invited_by=invited_by,
                expires_at=datetime.utcnow() + timedelta(days=7)  # 7 days expiry
            )

            self.db.add(invitation)
            self.db.commit()
            self.db.refresh(invitation)

            # TODO: Send invitation email

            return invitation

    def get_invitation_by_token(self, token: str) -> Optional[Invitation]:
        """Get invitation by token"""
        return self.db.query(Invitation).filter(
            Invitation.token == token
        ).first()

    def accept_invitation(self, accept_data: InvitationAccept) -> User:
        """Accept an invitation and create user account"""
        invitation = self.get_invitation_by_token(accept_data.token)

        if not invitation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invalid invitation token"
            )

        if not invitation.is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invitation has expired or already been used"
            )

        # Create user account
        auth_service = AuthService(self.db)
        user_data = UserCreate(
            email=invitation.email,
            password=accept_data.password,
            first_name=accept_data.first_name or invitation.first_name,
            last_name=accept_data.last_name or invitation.last_name,
            role=invitation.role,
            tenant_id=invitation.tenant_id
        )

        user = auth_service.create_user(user_data)

        # Mark invitation as used
        invitation.is_used = True
        invitation.used_at = datetime.utcnow()
        self.db.commit()

        return user

    def get_tenant_invitations(self, tenant_id: str, skip: int = 0, limit: int = 100) -> List[Invitation]:
        """Get all invitations for a tenant"""
        with tenant_context(self.db, tenant_id):
            return self.db.query(Invitation).filter(
                Invitation.tenant_id == tenant_id
            ).order_by(Invitation.created_at.desc()).offset(skip).limit(limit).all()

    def cancel_invitation(self, invitation_id: str, tenant_id: str) -> bool:
        """Cancel an invitation"""
        with tenant_context(self.db, tenant_id):
            invitation = self.db.query(Invitation).filter(
                Invitation.id == invitation_id,
                Invitation.tenant_id == tenant_id
            ).first()

            if not invitation:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Invitation not found"
                )

            if invitation.is_used:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Cannot cancel used invitation"
                )

            self.db.delete(invitation)
            self.db.commit()

            return True
```

## Step 7: Testing Implementation

### 7.1 Create Sprint 2 Tests
Create `backend/tests/test_sprint2.py`:
```python
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from src.models.tenant import Tenant
from src.models.user import User
from src.models.invitation import Invitation
from src.services.auth_service import AuthService
from src.services.tenant_service import TenantService
from src.services.invitation_service import InvitationService
from src.schemas.tenant import TenantCreate
from src.schemas.auth import UserCreate
from src.schemas.invitation import InvitationCreate

def test_tenant_creation(test_db: Session):
    """Test tenant creation with owner"""
    tenant_service = TenantService(test_db)

    tenant_data = TenantCreate(
        name="Test University",
        slug="test-university",
        owner_email="<EMAIL>",
        owner_password="testpassword123",
        owner_first_name="John",
        owner_last_name="Doe"
    )

    tenant = tenant_service.create_tenant(tenant_data)

    assert tenant.name == "Test University"
    assert tenant.slug == "test-university"

    # Verify owner was created
    owner = test_db.query(User).filter(
        User.tenant_id == tenant.id,
        User.role == "owner"
    ).first()

    assert owner is not None
    assert owner.email == "<EMAIL>"

def test_user_authentication(test_db: Session, sample_tenant: Tenant):
    """Test user authentication"""
    auth_service = AuthService(test_db)

    # Create a user
    user_data = UserCreate(
        email="<EMAIL>",
        password="testpassword123",
        tenant_id=sample_tenant.id
    )

    user = auth_service.create_user(user_data)

    # Test authentication
    from src.schemas.auth import LoginRequest
    login_data = LoginRequest(
        email="<EMAIL>",
        password="testpassword123",
        tenant_slug=sample_tenant.slug
    )

    authenticated_user = auth_service.authenticate_user(login_data)

    assert authenticated_user is not None
    assert authenticated_user.id == user.id

def test_invitation_flow(test_db: Session, sample_tenant: Tenant):
    """Test invitation creation and acceptance"""
    # Create an admin user first
    auth_service = AuthService(test_db)
    admin_data = UserCreate(
        email="<EMAIL>",
        password="adminpassword123",
        role="admin",
        tenant_id=sample_tenant.id
    )
    admin = auth_service.create_user(admin_data)

    # Create invitation
    invitation_service = InvitationService(test_db)
    invitation_data = InvitationCreate(
        email="<EMAIL>",
        role="user",
        first_name="Jane",
        last_name="Smith"
    )

    invitation = invitation_service.create_invitation(
        invitation_data,
        sample_tenant.id,
        admin.id
    )

    assert invitation.email == "<EMAIL>"
    assert invitation.is_valid

    # Accept invitation
    from src.schemas.invitation import InvitationAccept
    accept_data = InvitationAccept(
        token=invitation.token,
        password="newuserpassword123"
    )

    user = invitation_service.accept_invitation(accept_data)

    assert user.email == "<EMAIL>"
    assert user.tenant_id == sample_tenant.id

    # Verify invitation is marked as used
    test_db.refresh(invitation)
    assert invitation.is_used

def test_api_endpoints(client: TestClient, sample_tenant: Tenant):
    """Test API endpoints"""
    # Test health check
    response = client.get("/health")
    assert response.status_code == 200

    # Test login endpoint
    login_data = {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "tenant_slug": sample_tenant.slug
    }

    # First create a user
    auth_service = AuthService(client.app.dependency_overrides[get_db]())
    user_data = UserCreate(
        email="<EMAIL>",
        password="testpassword123",
        tenant_id=sample_tenant.id
    )
    auth_service.create_user(user_data)

    # Test login
    response = client.post("/api/v1/auth/login", json=login_data)
    assert response.status_code == 200

    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"
```

## Step 8: Validation and Deployment

### 8.1 Create Validation Script
Create `scripts/validate-sprint2.sh`:
```bash
#!/bin/bash

echo "Validating Sprint 2 implementation..."

# Run tests
echo "Running backend tests..."
docker-compose exec backend pytest tests/test_sprint2.py -v

# Test API endpoints
echo "Testing API endpoints..."
curl -f http://localhost:8000/health || exit 1
curl -f http://localhost:8000/api/v1/docs || exit 1

# Test database migrations
echo "Testing database state..."
docker-compose exec postgres psql -U postgres -d arroyo_university -c "SELECT COUNT(*) FROM tenants;"
docker-compose exec postgres psql -U postgres -d arroyo_university -c "SELECT COUNT(*) FROM users;"
docker-compose exec postgres psql -U postgres -d arroyo_university -c "SELECT COUNT(*) FROM invitations;"

echo "Sprint 2 validation complete!"
```

### 8.2 Update Makefile
Add to existing `Makefile`:
```makefile
test-sprint2:
	@docker-compose exec backend pytest tests/test_sprint2.py -v

validate-sprint2:
	@chmod +x scripts/validate-sprint2.sh
	@./scripts/validate-sprint2.sh

seed-data:
	@docker-compose exec backend python scripts/seed_data.py
```

## Validation Checklist

### 8.3 Sprint 2 Validation
- [ ] JWT authentication works correctly
- [ ] Tenant CRUD operations function properly
- [ ] User management endpoints are accessible
- [ ] Admin interface responds correctly
- [ ] Invitation system creates and accepts invitations
- [ ] CI/CD pipeline runs successfully
- [ ] All tests pass
- [ ] API documentation is accessible
- [ ] Database RLS policies work with new tables

## Next Steps

After completing Sprint 2:
1. Verify all authentication flows work
2. Test tenant isolation thoroughly
3. Confirm admin interface functionality
4. Validate invitation email flow (when email service is configured)
5. Proceed to Sprint 3: Question Bank Implementation

## Security Considerations

- JWT tokens include tenant context
- All database operations respect RLS policies
- Password hashing uses bcrypt
- Invitation tokens are cryptographically secure
- Admin endpoints require proper authorization
- Sensitive data is not logged
```
```
```
```
