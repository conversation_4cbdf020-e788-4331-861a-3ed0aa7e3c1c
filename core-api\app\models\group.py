"""
Group and group membership models
"""

from datetime import datetime
from typing import Optional, List
from uuid import UUID
from enum import Enum

from sqlmodel import SQLModel, Field, Column
from sqlalchemy.dialects.postgresql import JSONB

from .base import TimestampMixin, TenantMixin, MetadataMixin


class GroupType(str, Enum):
    """Group type enumeration"""
    PUBLIC = "public"
    PRIVATE = "private"


class MemberRole(str, Enum):
    """Member role enumeration"""
    LEADER = "leader"
    MEMBER = "member"


class InvitationStatus(str, Enum):
    """Invitation status enumeration"""
    PENDING = "pending"
    ACCEPTED = "accepted"
    DECLINED = "declined"
    EXPIRED = "expired"


class GroupBase(SQLModel):
    """Base group model"""
    name: str = Field(max_length=255)
    description: Optional[str] = None
    category: Optional[str] = Field(default=None, max_length=100)
    group_type: GroupType = Field(default=GroupType.PUBLIC)
    max_members: Optional[int] = Field(default=None, ge=1)
    is_active: bool = Field(default=True)


class Group(GroupBase, TenantMixin, TimestampMixin, table=True):
    """Group table model"""
    __tablename__ = "groups"
    
    group_id: UUID = Field(primary_key=True)
    created_by: UUID = Field(foreign_key="users.user_id", index=True)
    avatar_url: Optional[str] = Field(default=None, max_length=500)
    tags: Optional[List[str]] = Field(default_factory=list, sa_column=Column(JSONB))
    settings: Optional[dict] = Field(default_factory=dict, sa_column=Column(JSONB))


class GroupCreate(GroupBase):
    """Group creation model"""
    tags: Optional[List[str]] = Field(default_factory=list)


class GroupUpdate(SQLModel):
    """Group update model"""
    name: Optional[str] = Field(default=None, max_length=255)
    description: Optional[str] = None
    category: Optional[str] = Field(default=None, max_length=100)
    group_type: Optional[GroupType] = None
    max_members: Optional[int] = Field(default=None, ge=1)
    is_active: Optional[bool] = None
    avatar_url: Optional[str] = Field(default=None, max_length=500)
    tags: Optional[List[str]] = None


class GroupResponse(GroupBase):
    """Group response model"""
    group_id: UUID
    created_by: UUID
    avatar_url: Optional[str] = None
    tags: List[str]
    created_at: datetime
    updated_at: datetime
    member_count: Optional[int] = None
    leader_name: Optional[str] = None
    is_member: Optional[bool] = None
    is_leader: Optional[bool] = None


class GroupMemberBase(SQLModel):
    """Base group member model"""
    role: MemberRole = Field(default=MemberRole.MEMBER)
    joined_at: datetime = Field(default_factory=datetime.utcnow)
    is_active: bool = Field(default=True)


class GroupMember(GroupMemberBase, TenantMixin, table=True):
    """Group member table model"""
    __tablename__ = "group_members"
    
    group_id: UUID = Field(foreign_key="groups.group_id", primary_key=True)
    user_id: UUID = Field(foreign_key="users.user_id", primary_key=True)
    invited_by: Optional[UUID] = Field(foreign_key="users.user_id", default=None)
    left_at: Optional[datetime] = None


class GroupMemberCreate(SQLModel):
    """Group member creation model"""
    user_id: UUID
    role: MemberRole = Field(default=MemberRole.MEMBER)


class GroupMemberUpdate(SQLModel):
    """Group member update model"""
    role: Optional[MemberRole] = None
    is_active: Optional[bool] = None


class GroupMemberResponse(GroupMemberBase):
    """Group member response model"""
    group_id: UUID
    user_id: UUID
    invited_by: Optional[UUID] = None
    left_at: Optional[datetime] = None
    user_name: Optional[str] = None
    user_email: Optional[str] = None
    user_avatar: Optional[str] = None
    inviter_name: Optional[str] = None


class GroupInvitationBase(SQLModel):
    """Base group invitation model"""
    email: str = Field(max_length=255)
    message: Optional[str] = Field(default=None, max_length=500)
    status: InvitationStatus = Field(default=InvitationStatus.PENDING)
    expires_at: datetime


class GroupInvitation(GroupInvitationBase, TenantMixin, TimestampMixin, table=True):
    """Group invitation table model"""
    __tablename__ = "group_invitations"
    
    invitation_id: UUID = Field(primary_key=True)
    group_id: UUID = Field(foreign_key="groups.group_id", index=True)
    invited_by: UUID = Field(foreign_key="users.user_id", index=True)
    user_id: Optional[UUID] = Field(foreign_key="users.user_id", default=None, index=True)
    token: str = Field(max_length=255, unique=True)
    responded_at: Optional[datetime] = None


class GroupInvitationCreate(SQLModel):
    """Group invitation creation model"""
    emails: List[str]
    message: Optional[str] = Field(default=None, max_length=500)


class GroupInvitationResponse(GroupInvitationBase):
    """Group invitation response model"""
    invitation_id: UUID
    group_id: UUID
    invited_by: UUID
    user_id: Optional[UUID] = None
    token: str
    responded_at: Optional[datetime] = None
    created_at: datetime
    group_name: Optional[str] = None
    inviter_name: Optional[str] = None


class GroupInvitationAccept(SQLModel):
    """Group invitation accept model"""
    token: str


class GroupActivity(SQLModel, table=True):
    """Group activity model"""
    __tablename__ = "group_activities"
    
    activity_id: UUID = Field(primary_key=True)
    group_id: UUID = Field(foreign_key="groups.group_id", index=True)
    user_id: UUID = Field(foreign_key="users.user_id", index=True)
    tenant_id: UUID = Field(foreign_key="tenants.tenant_id", index=True)
    activity_type: str = Field(max_length=50)  # 'member_joined', 'member_left', 'discussion_created', etc.
    description: str = Field(max_length=500)
    activity_data: Optional[dict] = Field(default_factory=dict, sa_column=Column(JSONB))
    created_at: datetime = Field(default_factory=datetime.utcnow)


class GroupActivityCreate(SQLModel):
    """Group activity creation model"""
    activity_type: str = Field(max_length=50)
    description: str = Field(max_length=500)
    activity_data: Optional[dict] = Field(default_factory=dict)


class GroupActivityResponse(SQLModel):
    """Group activity response model"""
    activity_id: UUID
    group_id: UUID
    user_id: UUID
    activity_type: str
    description: str
    activity_data: dict
    created_at: datetime
    user_name: Optional[str] = None
    user_avatar: Optional[str] = None


class GroupStatistics(SQLModel):
    """Group statistics model"""
    group_id: UUID
    total_members: int
    active_members: int
    new_members_this_week: int
    total_activities: int
    activities_this_week: int
    average_engagement: float
    top_contributors: List[dict]


class GroupSearch(SQLModel):
    """Group search model"""
    query: Optional[str] = None
    category: Optional[str] = None
    group_type: Optional[GroupType] = None
    tags: Optional[List[str]] = None
    min_members: Optional[int] = None
    max_members: Optional[int] = None
