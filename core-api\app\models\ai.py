"""
AI service models for question generation, scoring, and content processing
"""

from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from uuid import UUID
from enum import Enum

from sqlmodel import SQLModel, Field, Column
from sqlalchemy.dialects.postgresql import JSONB
from pydantic import BaseModel

from .base import TimestampMixin, TenantMixin


class AITaskType(str, Enum):
    """AI task types"""
    QUESTION_GENERATION = "question_generation"
    CONTENT_SCORING = "content_scoring"
    CONTENT_MODERATION = "content_moderation"
    SPEECH_SYNTHESIS = "speech_synthesis"
    SPEECH_RECOGNITION = "speech_recognition"
    PLAGIARISM_DETECTION = "plagiarism_detection"


class AITaskStatus(str, Enum):
    """AI task status"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class QuestionType(str, Enum):
    """Question types for AI generation"""
    MULTIPLE_CHOICE = "multiple_choice"
    TRUE_FALSE = "true_false"
    ESSAY = "essay"
    FILL_IN_BLANK = "fill_in_blank"
    LISTENING = "listening"
    SPEAKING = "speaking"
    READING = "reading"
    WRITING = "writing"


class DifficultyLevel(str, Enum):
    """Difficulty levels"""
    BEGINNER = "beginner"
    ELEMENTARY = "elementary"
    INTERMEDIATE = "intermediate"
    UPPER_INTERMEDIATE = "upper_intermediate"
    ADVANCED = "advanced"
    PROFICIENT = "proficient"


# AI Task Management Models
class AITaskBase(SQLModel):
    """Base AI task model"""
    task_type: AITaskType
    status: AITaskStatus = Field(default=AITaskStatus.PENDING)
    input_data: Dict[str, Any] = Field(default_factory=dict, sa_column=Column(JSONB))
    output_data: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSONB))
    error_message: Optional[str] = None
    processing_time_seconds: Optional[float] = None
    cost_usd: Optional[float] = None
    model_used: Optional[str] = None
    tokens_used: Optional[int] = None


class AITask(AITaskBase, TenantMixin, TimestampMixin, table=True):
    """AI task table model"""
    __tablename__ = "ai_tasks"
    
    task_id: UUID = Field(primary_key=True)
    user_id: Optional[UUID] = Field(foreign_key="users.user_id", default=None)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


class AITaskCreate(AITaskBase):
    """AI task creation model"""
    user_id: Optional[UUID] = None


class AITaskResponse(AITaskBase):
    """AI task response model"""
    task_id: UUID
    user_id: Optional[UUID]
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime


# Question Generation Models
class QuestionGenerationRequest(BaseModel):
    """Request for AI question generation"""
    topic: str
    question_type: QuestionType
    difficulty_level: DifficultyLevel
    count: int = Field(ge=1, le=10)
    language: str = "en"
    context: Optional[str] = None
    learning_objectives: Optional[List[str]] = None
    additional_instructions: Optional[str] = None


class GeneratedQuestion(BaseModel):
    """Generated question model"""
    title: str
    content: str
    question_type: QuestionType
    difficulty: DifficultyLevel
    points: int = 1
    time_limit_seconds: Optional[int] = None
    explanation: Optional[str] = None
    question_data: Dict[str, Any]  # Type-specific data (options, correct answers, etc.)
    tags: List[str] = []
    estimated_time_minutes: Optional[int] = None


class QuestionGenerationResponse(BaseModel):
    """Response for question generation"""
    task_id: UUID
    questions: List[GeneratedQuestion]
    generation_time_seconds: float
    model_used: str
    tokens_used: int
    cost_usd: float


# Content Scoring Models
class ScoringCriteria(BaseModel):
    """Scoring criteria for content evaluation"""
    criterion: str
    weight: float = 1.0
    max_score: float = 5.0
    description: Optional[str] = None


class ScoringRequest(BaseModel):
    """Request for AI content scoring"""
    content: str
    question_context: str
    scoring_criteria: List[ScoringCriteria]
    max_score: float = 100.0
    language: str = "en"
    rubric_type: str = "holistic"  # holistic, analytic
    include_feedback: bool = True


class ScoringResult(BaseModel):
    """Result of AI content scoring"""
    overall_score: float
    max_score: float
    percentage: float
    rubric_breakdown: Dict[str, float]
    feedback: str
    strengths: List[str] = []
    areas_for_improvement: List[str] = []
    confidence: float  # 0.0 to 1.0
    estimated_level: Optional[DifficultyLevel] = None


class ScoringResponse(BaseModel):
    """Response for content scoring"""
    task_id: UUID
    result: ScoringResult
    processing_time_seconds: float
    model_used: str
    tokens_used: int
    cost_usd: float


# Content Moderation Models
class ModerationRequest(BaseModel):
    """Request for content moderation"""
    content: str
    context: Optional[str] = None
    check_categories: List[str] = ["hate", "harassment", "self-harm", "sexual", "violence"]
    threshold: float = 0.8


class ModerationFlag(BaseModel):
    """Moderation flag result"""
    category: str
    flagged: bool
    score: float
    severity: str  # low, medium, high, critical


class ModerationResult(BaseModel):
    """Result of content moderation"""
    flagged: bool
    overall_score: float
    flags: List[ModerationFlag]
    filtered_content: Optional[str] = None
    reason: Optional[str] = None


class ModerationResponse(BaseModel):
    """Response for content moderation"""
    task_id: UUID
    result: ModerationResult
    processing_time_seconds: float


# Speech Services Models
class SpeechSynthesisRequest(BaseModel):
    """Request for text-to-speech synthesis"""
    text: str
    voice: str = "en-US-AriaNeural"
    language: str = "en-US"
    speed: float = 1.0
    pitch: float = 0.0
    output_format: str = "mp3"  # mp3, wav, ogg


class SpeechSynthesisResponse(BaseModel):
    """Response for speech synthesis"""
    task_id: UUID
    audio_url: str
    duration_seconds: float
    file_size_bytes: int
    processing_time_seconds: float


class SpeechRecognitionRequest(BaseModel):
    """Request for speech-to-text recognition"""
    audio_url: str
    language: str = "en-US"
    enable_punctuation: bool = True
    enable_profanity_filter: bool = True


class SpeechRecognitionResponse(BaseModel):
    """Response for speech recognition"""
    task_id: UUID
    transcript: str
    confidence: float
    duration_seconds: float
    processing_time_seconds: float


# Plagiarism Detection Models
class PlagiarismCheckRequest(BaseModel):
    """Request for plagiarism detection"""
    content: str
    title: Optional[str] = None
    author: Optional[str] = None
    check_internet: bool = True
    check_database: bool = True
    similarity_threshold: float = 0.7


class PlagiarismMatch(BaseModel):
    """Plagiarism match result"""
    source_title: str
    source_url: Optional[str] = None
    similarity_percentage: float
    matched_text: str
    source_type: str  # internet, database, submission


class PlagiarismResult(BaseModel):
    """Result of plagiarism detection"""
    overall_similarity: float
    is_plagiarized: bool
    matches: List[PlagiarismMatch]
    original_percentage: float
    risk_level: str  # low, medium, high, critical


class PlagiarismResponse(BaseModel):
    """Response for plagiarism detection"""
    task_id: UUID
    result: PlagiarismResult
    processing_time_seconds: float


# AI Usage Analytics Models
class AIUsageStats(BaseModel):
    """AI usage statistics"""
    tenant_id: UUID
    date: datetime
    task_type: AITaskType
    total_requests: int
    successful_requests: int
    failed_requests: int
    total_tokens_used: int
    total_cost_usd: float
    average_processing_time: float


class AIQuotaLimit(SQLModel, table=True):
    """AI quota limits per tenant"""
    __tablename__ = "ai_quota_limits"
    
    tenant_id: UUID = Field(primary_key=True, foreign_key="tenants.tenant_id")
    daily_requests_limit: int = Field(default=1000)
    monthly_requests_limit: int = Field(default=10000)
    daily_cost_limit_usd: float = Field(default=100.0)
    monthly_cost_limit_usd: float = Field(default=1000.0)
    daily_requests_used: int = Field(default=0)
    monthly_requests_used: int = Field(default=0)
    daily_cost_used_usd: float = Field(default=0.0)
    monthly_cost_used_usd: float = Field(default=0.0)
    last_reset_date: datetime = Field(default_factory=datetime.utcnow)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


# AI Configuration Models
class AIProviderConfig(BaseModel):
    """AI provider configuration"""
    provider: str  # openai, azure, anthropic
    model: str
    api_key: str
    endpoint: Optional[str] = None
    max_tokens: int = 4000
    temperature: float = 0.7
    timeout_seconds: int = 30
    max_retries: int = 3
    enabled: bool = True


class AIServiceConfig(BaseModel):
    """AI service configuration"""
    question_generation: AIProviderConfig
    content_scoring: AIProviderConfig
    content_moderation: AIProviderConfig
    speech_synthesis: AIProviderConfig
    speech_recognition: AIProviderConfig
