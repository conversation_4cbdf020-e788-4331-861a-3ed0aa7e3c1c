import { apiClient } from './api';
import { User } from '@/types';

export interface CreateUserRequest {
  email: string;
  role: string;
  firstName?: string;
  lastName?: string;
}

export interface BulkCreateUsersRequest {
  emails: string[];
  role: string;
}

export interface UpdateUserRequest {
  firstName?: string;
  lastName?: string;
  role?: string;
  status?: 'active' | 'inactive' | 'suspended';
}

export interface UserFilters {
  search?: string;
  role?: string;
  status?: 'active' | 'inactive' | 'pending' | 'suspended';
  createdBy?: 'manual' | 'csv' | 'invitation';
}

export interface UserStats {
  totalUsers: number;
  activeUsers: number;
  pendingUsers: number;
  inactiveUsers: number;
  suspendedUsers: number;
  usersByRole: Record<string, number>;
  recentSignups: number;
}

export interface BulkCreateResult {
  created: number;
  failed: number;
  errors: string[];
  users: User[];
}

export const userService = {
  // Get all users with optional filters (Admin only)
  async getUsers(filters?: UserFilters): Promise<User[]> {
    const params = new URLSearchParams();
    if (filters?.search) params.append('search', filters.search);
    if (filters?.role) params.append('role', filters.role);
    if (filters?.status) params.append('status', filters.status);
    if (filters?.createdBy) params.append('createdBy', filters.createdBy);

    const response = await apiClient.get(`/admin/users?${params.toString()}`);
    return response.data;
  },

  // Get user by ID
  async getUser(id: string): Promise<User> {
    const response = await apiClient.get(`/admin/users/${id}`);
    return response.data;
  },

  // Create single user (Admin only)
  async createUser(data: CreateUserRequest): Promise<User> {
    const response = await apiClient.post('/admin/users', data);
    return response.data;
  },

  // Create multiple users (Admin only)
  async createUsersInBulk(data: BulkCreateUsersRequest): Promise<BulkCreateResult> {
    const response = await apiClient.post('/admin/users/bulk', data);
    return response.data;
  },

  // Update user (Admin only)
  async updateUser(id: string, data: UpdateUserRequest): Promise<User> {
    const response = await apiClient.put(`/admin/users/${id}`, data);
    return response.data;
  },

  // Delete user (Admin only)
  async deleteUser(id: string): Promise<void> {
    await apiClient.delete(`/admin/users/${id}`);
  },

  // Activate/Deactivate user (Admin only)
  async toggleUserStatus(id: string): Promise<User> {
    const response = await apiClient.post(`/admin/users/${id}/toggle-status`);
    return response.data;
  },

  // Suspend user (Admin only)
  async suspendUser(id: string, reason?: string): Promise<User> {
    const response = await apiClient.post(`/admin/users/${id}/suspend`, { reason });
    return response.data;
  },

  // Unsuspend user (Admin only)
  async unsuspendUser(id: string): Promise<User> {
    const response = await apiClient.post(`/admin/users/${id}/unsuspend`);
    return response.data;
  },

  // Resend password to user (Admin only)
  async resendPassword(id: string): Promise<void> {
    await apiClient.post(`/admin/users/${id}/resend-password`);
  },

  // Reset user password (Admin only)
  async resetUserPassword(id: string): Promise<{ temporaryPassword: string }> {
    const response = await apiClient.post(`/admin/users/${id}/reset-password`);
    return response.data;
  },

  // Get user statistics (Admin only)
  async getUserStats(): Promise<UserStats> {
    const response = await apiClient.get('/admin/users/stats');
    return response.data;
  },

  // Get available roles
  async getRoles(): Promise<string[]> {
    const response = await apiClient.get('/admin/roles');
    return response.data;
  },

  // Assign role to user (Admin only)
  async assignRole(userId: string, role: string): Promise<User> {
    const response = await apiClient.post(`/admin/users/${userId}/assign-role`, { role });
    return response.data;
  },

  // Remove role from user (Admin only)
  async removeRole(userId: string, role: string): Promise<User> {
    const response = await apiClient.post(`/admin/users/${userId}/remove-role`, { role });
    return response.data;
  },

  // Import users from CSV (Admin only)
  async importUsersFromCSV(file: File, role: string): Promise<BulkCreateResult> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('role', role);

    const response = await apiClient.post('/admin/users/import-csv', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Export users to CSV (Admin only)
  async exportUsersToCSV(filters?: UserFilters): Promise<Blob> {
    const params = new URLSearchParams();
    if (filters?.search) params.append('search', filters.search);
    if (filters?.role) params.append('role', filters.role);
    if (filters?.status) params.append('status', filters.status);

    const response = await apiClient.get(`/admin/users/export-csv?${params.toString()}`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // Get user activity log (Admin only)
  async getUserActivity(userId: string): Promise<any[]> {
    const response = await apiClient.get(`/admin/users/${userId}/activity`);
    return response.data;
  },

  // Send notification to user (Admin only)
  async sendNotificationToUser(userId: string, message: string, type: 'info' | 'warning' | 'success' | 'error' = 'info'): Promise<void> {
    await apiClient.post(`/admin/users/${userId}/notify`, { message, type });
  },

  // Send notification to multiple users (Admin only)
  async sendBulkNotification(userIds: string[], message: string, type: 'info' | 'warning' | 'success' | 'error' = 'info'): Promise<void> {
    await apiClient.post('/admin/users/bulk-notify', { userIds, message, type });
  },

  // Get user's courses (Admin only)
  async getUserCourses(userId: string): Promise<any[]> {
    const response = await apiClient.get(`/admin/users/${userId}/courses`);
    return response.data;
  },

  // Get user's groups (Admin only)
  async getUserGroups(userId: string): Promise<any[]> {
    const response = await apiClient.get(`/admin/users/${userId}/groups`);
    return response.data;
  },
};
