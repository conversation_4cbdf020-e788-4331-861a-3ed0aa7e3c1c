"""
Courses router for course management
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlmodel import Session
from typing import Optional, List
from uuid import UUID

from ..core.database import get_session
from ..core.security import get_current_user, Permissions, require_permissions
from ..services.course_service import CourseService
from ..models.course import (
    CourseCreate, CourseUpdate, CourseResponse, CourseStatus,
    CourseModuleCreate, CourseRatingCreate
)
from ..models.base import SuccessResponse

router = APIRouter()


def get_course_service(db: Session = Depends(get_session)) -> CourseService:
    """Get course service"""
    return CourseService(db)


@router.get("/", response_model=List[CourseResponse])
async def list_courses(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    category: Optional[str] = Query(None),
    difficulty: Optional[str] = Query(None),
    instructor_id: Optional[UUID] = Query(None),
    status: Optional[CourseStatus] = Query(None),
    is_public: Optional[bool] = Query(None),
    current_user = Depends(get_current_user),
    course_service: CourseService = Depends(get_course_service)
):
    """List courses with filtering"""
    try:
        courses = await course_service.list_courses(
            tenant_id=current_user.tenant_id,
            skip=skip,
            limit=limit,
            search=search,
            category=category,
            difficulty=difficulty,
            instructor_id=instructor_id,
            status=status,
            is_public=is_public
        )

        return courses

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve courses"
        )


@router.get("/{course_id}", response_model=CourseResponse)
async def get_course(
    course_id: UUID,
    current_user = Depends(get_current_user),
    course_service: CourseService = Depends(get_course_service)
):
    """Get course by ID"""
    try:
        course = await course_service.get_course(course_id, current_user.tenant_id)

        if not course:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Course not found"
            )

        return course

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve course"
        )


@router.post("/", response_model=CourseResponse)
@require_permissions([Permissions.COURSE_CREATE])
async def create_course(
    course_data: CourseCreate,
    current_user = Depends(get_current_user),
    course_service: CourseService = Depends(get_course_service)
):
    """Create a new course"""
    try:
        course = await course_service.create_course(
            course_data=course_data,
            instructor_id=current_user.user_id,
            tenant_id=current_user.tenant_id
        )

        return await course_service.get_course(course.course_id, current_user.tenant_id)

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create course"
        )


@router.put("/{course_id}", response_model=CourseResponse)
@require_permissions([Permissions.COURSE_UPDATE])
async def update_course(
    course_id: UUID,
    course_data: CourseUpdate,
    current_user = Depends(get_current_user),
    course_service: CourseService = Depends(get_course_service)
):
    """Update course"""
    try:
        course = await course_service.update_course(
            course_id=course_id,
            course_data=course_data,
            tenant_id=current_user.tenant_id
        )

        if not course:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Course not found"
            )

        return await course_service.get_course(course.course_id, current_user.tenant_id)

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/{course_id}")
@require_permissions([Permissions.COURSE_DELETE])
async def delete_course(
    course_id: UUID,
    current_user = Depends(get_current_user),
    course_service: CourseService = Depends(get_course_service)
):
    """Delete course"""
    try:
        success = await course_service.delete_course(
            course_id=course_id,
            tenant_id=current_user.tenant_id
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Course not found"
            )

        return SuccessResponse(message="Course deleted successfully")

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/{course_id}/publish")
@require_permissions([Permissions.COURSE_PUBLISH])
async def publish_course(
    course_id: UUID,
    current_user = Depends(get_current_user),
    course_service: CourseService = Depends(get_course_service)
):
    """Publish course"""
    try:
        success = await course_service.publish_course(
            course_id=course_id,
            tenant_id=current_user.tenant_id
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Course not found"
            )

        return SuccessResponse(message="Course published successfully")

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to publish course"
        )


@router.post("/{course_id}/enroll")
async def enroll_in_course(
    course_id: UUID,
    current_user = Depends(get_current_user),
    course_service: CourseService = Depends(get_course_service)
):
    """Enroll current user in course"""
    try:
        success = await course_service.enroll_user(
            course_id=course_id,
            user_id=current_user.user_id,
            tenant_id=current_user.tenant_id
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Already enrolled or course not found"
            )

        return SuccessResponse(message="Enrolled successfully")

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to enroll in course"
        )


@router.post("/{course_id}/modules")
@require_permissions([Permissions.COURSE_UPDATE])
async def add_course_module(
    course_id: UUID,
    module_data: CourseModuleCreate,
    current_user = Depends(get_current_user),
    course_service: CourseService = Depends(get_course_service)
):
    """Add module to course"""
    try:
        module_data.course_id = course_id
        module = await course_service.add_module(
            module_data=module_data,
            tenant_id=current_user.tenant_id
        )

        return {
            "success": True,
            "message": "Module added successfully",
            "data": module
        }

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/{course_id}/rate")
async def rate_course(
    course_id: UUID,
    rating_data: CourseRatingCreate,
    current_user = Depends(get_current_user),
    course_service: CourseService = Depends(get_course_service)
):
    """Rate a course"""
    try:
        rating = await course_service.rate_course(
            course_id=course_id,
            user_id=current_user.user_id,
            rating_data=rating_data,
            tenant_id=current_user.tenant_id
        )

        return {
            "success": True,
            "message": "Course rated successfully",
            "data": rating
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to rate course"
        )


@router.get("/recent", response_model=List[CourseResponse])
async def get_recent_courses(
    limit: int = Query(5, ge=1, le=20),
    current_user = Depends(get_current_user),
    course_service: CourseService = Depends(get_course_service)
):
    """Get user's recent courses for dashboard"""
    try:
        courses = await course_service.get_user_recent_courses(
            user_id=current_user.user_id,
            tenant_id=current_user.tenant_id,
            limit=limit
        )

        return courses

    except Exception as e:
        # Return empty list if there's an error
        return []


@router.get("/recommended", response_model=List[CourseResponse])
async def get_recommended_courses(
    limit: int = Query(5, ge=1, le=20),
    current_user = Depends(get_current_user),
    course_service: CourseService = Depends(get_course_service)
):
    """Get recommended courses for user dashboard"""
    try:
        courses = await course_service.get_recommended_courses(
            user_id=current_user.user_id,
            tenant_id=current_user.tenant_id,
            limit=limit
        )

        return courses

    except Exception as e:
        # Return empty list if there's an error
        return []


@router.get("/featured", response_model=List[CourseResponse])
async def get_featured_courses(
    limit: int = Query(10, ge=1, le=50),
    current_user = Depends(get_current_user),
    course_service: CourseService = Depends(get_course_service)
):
    """Get featured courses"""
    try:
        courses = await course_service.get_featured_courses(
            tenant_id=current_user.tenant_id,
            limit=limit
        )

        return courses

    except Exception as e:
        # Return empty list if there's an error
        return []
