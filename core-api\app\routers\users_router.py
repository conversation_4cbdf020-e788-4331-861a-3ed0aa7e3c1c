"""
Users router for user management
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlmodel import Session
from typing import Optional, List
from uuid import UUID

from ..core.database import get_session
from ..services.auth_service import AuthService
from ..services.user_service import UserService
from ..models.user import UserCreate, UserUpdate, UserResponse, UserProfile, UserChangePassword
from ..models.base import SuccessResponse, PaginatedResponse

router = APIRouter()
security = HTTPBearer()


def get_auth_service(db: Session = Depends(get_session)) -> AuthService:
    """Get authentication service"""
    return AuthService(db)


def get_user_service(db: Session = Depends(get_session)) -> UserService:
    """Get user service"""
    return UserService(db)


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    auth_service: AuthService = Depends(get_auth_service)
):
    """Get current authenticated user"""
    user = await auth_service.verify_token(credentials.credentials)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token"
        )
    return user


@router.post("/", response_model=UserResponse)
async def create_user(
    user_data: UserCreate,
    current_user = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    """Create a new user (admin only)"""
    try:
        # Check if current user has permission to create users
        # This would be implemented with proper role checking
        
        user = await user_service.create_user(
            user_data=user_data,
            tenant_id=current_user.tenant_id,
            created_by=current_user.user_id
        )
        
        return user
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/", response_model=List[UserResponse])
async def list_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    role_id: Optional[UUID] = Query(None),
    is_active: Optional[bool] = Query(None),
    current_user = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    """List users with filtering"""
    try:
        users = await user_service.list_users(
            tenant_id=current_user.tenant_id,
            skip=skip,
            limit=limit,
            search=search,
            role_id=role_id,
            is_active=is_active
        )
        
        return users
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve users"
        )


@router.get("/me", response_model=UserProfile)
async def get_my_profile(
    current_user = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    """Get current user's profile"""
    try:
        profile = await user_service.get_user_profile(
            user_id=current_user.user_id,
            tenant_id=current_user.tenant_id
        )
        
        if not profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Profile not found"
            )
        
        return profile
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve profile"
        )


@router.get("/{user_id}", response_model=UserProfile)
async def get_user_profile(
    user_id: UUID,
    current_user = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    """Get user profile by ID"""
    try:
        profile = await user_service.get_user_profile(
            user_id=user_id,
            tenant_id=current_user.tenant_id
        )
        
        if not profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        return profile
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user profile"
        )


@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: UUID,
    user_data: UserUpdate,
    current_user = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    """Update user"""
    try:
        # Check if user can update this profile
        if user_id != current_user.user_id:
            # Check admin permissions here
            pass
        
        user = await user_service.update_user(
            user_id=user_id,
            user_data=user_data,
            tenant_id=current_user.tenant_id
        )
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        return user
        
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/{user_id}")
async def delete_user(
    user_id: UUID,
    current_user = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    """Delete user (soft delete)"""
    try:
        # Check admin permissions here
        
        success = await user_service.delete_user(
            user_id=user_id,
            tenant_id=current_user.tenant_id
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        return SuccessResponse(message="User deleted successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete user"
        )


@router.post("/change-password")
async def change_password(
    password_data: UserChangePassword,
    current_user = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    """Change current user's password"""
    try:
        success = await user_service.change_password(
            user_id=current_user.user_id,
            password_data=password_data,
            tenant_id=current_user.tenant_id
        )
        
        if success:
            return SuccessResponse(message="Password changed successfully")
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to change password"
            )
            
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/{user_id}/assign-roles")
async def assign_user_roles(
    user_id: UUID,
    role_ids: List[UUID],
    current_user = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    """Assign roles to user (admin only)"""
    try:
        # Check admin permissions here
        
        success = await user_service.assign_roles(
            user_id=user_id,
            role_ids=role_ids,
            assigned_by=current_user.user_id
        )
        
        if success:
            return SuccessResponse(message="Roles assigned successfully")
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to assign roles"
            )
            
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to assign roles"
        )


@router.get("/stats")
async def get_user_stats(
    current_user = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    """Get current user's dashboard statistics"""
    try:
        stats = await user_service.get_user_stats(
            user_id=current_user.user_id,
            tenant_id=current_user.tenant_id
        )

        return {
            "completedCourses": stats.get("completed_courses", 0),
            "inProgressCourses": stats.get("in_progress_courses", 0),
            "savedCourses": stats.get("saved_courses", 0),
            "streakDays": stats.get("streak_days", 0)
        }

    except Exception as e:
        # Return default stats if there's an error
        return {
            "completedCourses": 0,
            "inProgressCourses": 0,
            "savedCourses": 0,
            "streakDays": 0
        }
