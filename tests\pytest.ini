[tool:pytest]
# Pytest configuration for ArroyoUniversity test suite

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Output options
addopts = 
    -v
    --strict-markers
    --strict-config
    --tb=short
    --color=yes
    --durations=10
    --showlocals

# Markers for test categorization
markers =
    infrastructure: Infrastructure and environment tests
    multi_tenant: Multi-tenant architecture tests
    database: Database and RLS tests
    integration: Integration tests
    performance: Performance tests
    security: Security tests
    acceptance: Acceptance criteria tests
    slow: Slow running tests
    unit: Unit tests
    functional: Functional tests

# Test filtering
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:sqlalchemy.*

# Minimum version requirements
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Coverage options (when using pytest-cov)
[coverage:run]
source = backend/src
omit = 
    */tests/*
    */venv/*
    */migrations/*
    */__pycache__/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError
    if __name__ == .__main__.:
    class .*\(Protocol\):
    @(abc\.)?abstractmethod

show_missing = True
precision = 2

[coverage:html]
directory = htmlcov
