-- Sample Data for Development and Testing
-- This script inserts sample data for all tables
-- NOTE: This file has been renamed to prevent automatic loading
-- To load sample data, manually execute this file or rename it back to 06-sample-data.sql

-- Insert sample tenant
INSERT INTO tenants (tenant_id, name, subdomain, description, status, plan) VALUES
('550e8400-e29b-41d4-a716-446655440000', 'Universidad Ejemplo', 'ejemplo', 'Universidad de ejemplo para desarrollo', 'active', 'premium');

-- Insert sample roles
INSERT INTO roles (role_id, tenant_id, name, description, permissions, is_system_role) VALUES
('550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440000', 'Owner', 'System owner with full access', '{"all": true}', true),
('550e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440000', 'Admin Tenant', 'Tenant administrator', '{"tenant:manage": true, "users:manage": true, "roles:manage": true}', true),
('550e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440000', 'Usuario', 'Regular user', '{"courses:view": true, "courses:enroll": true}', true),
('550e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440000', 'Instructor', 'Course instructor', '{"courses:create": true, "courses:edit_own": true, "courses:delete_own": true}', false);

-- Insert sample users
INSERT INTO users (user_id, tenant_id, email, username, first_name, last_name, status, email_verified) VALUES
('550e8400-e29b-41d4-a716-446655440010', '550e8400-e29b-41d4-a716-446655440000', '<EMAIL>', 'admin', 'Admin', 'Sistema', 'active', true),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440000', '<EMAIL>', 'instructor1', 'María', 'González', 'active', true),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440000', '<EMAIL>', 'estudiante1', 'Carlos', 'García', 'active', true),
('************************************', '550e8400-e29b-41d4-a716-446655440000', '<EMAIL>', 'experto1', 'Dr. Roberto', 'Díaz', 'active', true);

-- Assign roles to users
INSERT INTO user_roles (user_id, role_id) VALUES
('550e8400-e29b-41d4-a716-446655440010', '550e8400-e29b-41d4-a716-446655440002'), -- Admin
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440004'), -- Instructor
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440003'), -- Usuario
('************************************', '550e8400-e29b-41d4-a716-446655440003'); -- Usuario (Expert)

-- Insert expert areas
INSERT INTO expert_areas (user_id, area_name, assigned_by) VALUES
('************************************', 'DevOps', '550e8400-e29b-41d4-a716-446655440010'),
('************************************', 'Cloud Computing', '550e8400-e29b-41d4-a716-446655440010'),
('550e8400-e29b-41d4-a716-************', 'Programming', '550e8400-e29b-41d4-a716-446655440010');

-- Insert sample skills
INSERT INTO skills (skill_id, name, description, area, color_class, difficulty_level, estimated_learning_hours) VALUES
('550e8400-e29b-41d4-a716-446655440020', 'JavaScript', 'Programming language for web development', 'programming', 'bg-yellow-500', 'intermediate', 40),
('550e8400-e29b-41d4-a716-446655440021', 'Python', 'General-purpose programming language', 'programming', 'bg-green-500', 'beginner', 50),
('550e8400-e29b-41d4-a716-446655440022', 'React', 'JavaScript library for building user interfaces', 'programming', 'bg-cyan-500', 'intermediate', 35),
('550e8400-e29b-41d4-a716-446655440023', 'Docker', 'Containerization platform', 'devops', 'bg-blue-600', 'intermediate', 30),
('550e8400-e29b-41d4-a716-446655440024', 'Kubernetes', 'Container orchestration platform', 'devops', 'bg-indigo-600', 'advanced', 60),
('550e8400-e29b-41d4-a716-446655440025', 'SQL', 'Database query language', 'data', 'bg-gray-600', 'beginner', 25),
('550e8400-e29b-41d4-a716-446655440026', 'Machine Learning', 'AI and predictive modeling', 'data', 'bg-purple-600', 'advanced', 80);

-- Insert sample courses
INSERT INTO courses (course_id, tenant_id, title, description, creator_id, status, metadata) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440000', 'JavaScript Fundamentals', 'Learn the basics of JavaScript programming', '550e8400-e29b-41d4-a716-************', 'published', '{"difficulty": "beginner", "duration_hours": 20}'),
('550e8400-e29b-41d4-a716-446655440031', '550e8400-e29b-41d4-a716-446655440000', 'React for Beginners', 'Introduction to React library', '550e8400-e29b-41d4-a716-************', 'published', '{"difficulty": "intermediate", "duration_hours": 25}'),
('550e8400-e29b-41d4-a716-446655440032', '550e8400-e29b-41d4-a716-446655440000', 'Docker Essentials', 'Containerization with Docker', '************************************', 'published', '{"difficulty": "intermediate", "duration_hours": 15}');

-- Insert sample career paths
INSERT INTO career_paths (path_id, name, description, from_position, to_position, estimated_duration_months, created_by, is_template, skills_count) VALUES
('550e8400-e29b-41d4-a716-446655440040', 'Junior to Senior Developer', 'Complete path from junior to senior developer', 'Junior Developer', 'Senior Developer', 12, '550e8400-e29b-41d4-a716-************', true, 5),
('************************************', 'Sysadmin to DevOps Engineer', 'Transition from system administration to DevOps', 'System Administrator', 'DevOps Engineer', 10, '************************************', true, 4);

-- Insert career path skills
INSERT INTO career_path_skills (path_id, skill_id, position_x, position_y, is_starting_skill, order_sequence) VALUES
('550e8400-e29b-41d4-a716-446655440040', '550e8400-e29b-41d4-a716-446655440020', 100, 100, true, 1), -- JavaScript
('550e8400-e29b-41d4-a716-446655440040', '550e8400-e29b-41d4-a716-446655440022', 300, 100, false, 2), -- React
('550e8400-e29b-41d4-a716-446655440040', '550e8400-e29b-41d4-a716-446655440021', 200, 200, false, 3), -- Python
('************************************', '550e8400-e29b-41d4-a716-446655440023', 100, 100, true, 1), -- Docker
('************************************', '550e8400-e29b-41d4-a716-446655440024', 300, 100, false, 2); -- Kubernetes

-- Insert career path connections
INSERT INTO career_path_connections (path_id, from_skill_id, to_skill_id, connection_type) VALUES
('550e8400-e29b-41d4-a716-446655440040', '550e8400-e29b-41d4-a716-446655440020', '550e8400-e29b-41d4-a716-446655440022', 'prerequisite'),
('************************************', '550e8400-e29b-41d4-a716-446655440023', '550e8400-e29b-41d4-a716-446655440024', 'prerequisite');

-- Insert skill-course mappings
INSERT INTO skill_course_mappings (skill_id, course_id, relevance_score, is_primary) VALUES
('550e8400-e29b-41d4-a716-446655440020', '550e8400-e29b-41d4-a716-************', 1.0, true),
('550e8400-e29b-41d4-a716-446655440022', '550e8400-e29b-41d4-a716-446655440031', 1.0, true),
('550e8400-e29b-41d4-a716-446655440023', '550e8400-e29b-41d4-a716-446655440032', 1.0, true);

-- Insert user career path activations
INSERT INTO user_career_paths (user_id, path_id, skills_total, skills_completed, progress_percentage) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440040', 3, 1, 33.33);

-- Insert user skill progress
INSERT INTO user_skill_progress (user_id, skill_id, path_id, status, completion_date, verification_method) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440020', '550e8400-e29b-41d4-a716-446655440040', 'completed', CURRENT_TIMESTAMP, 'course_completion');

-- Insert course enrollments
INSERT INTO course_enrollments (user_id, course_id, progress_percentage, completed_at) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 100.0, CURRENT_TIMESTAMP),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440031', 45.0, NULL);

-- Insert course ratings
INSERT INTO course_ratings (course_id, user_id, rating_type) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'like'),
('550e8400-e29b-41d4-a716-446655440031', '550e8400-e29b-41d4-a716-************', 'like');

-- Insert expert reviews
INSERT INTO expert_reviews (course_id, expert_id, expert_area, rating, title, review_text, is_verified) VALUES
('550e8400-e29b-41d4-a716-446655440032', '************************************', 'DevOps', 'excelente', 'Excelente introducción a Docker', 'Este curso proporciona una base sólida en containerización con ejemplos prácticos y ejercicios bien estructurados.', true);

-- Insert sample scoring configuration
INSERT INTO scoring_config (config_key, config_value, description) VALUES
('base_points', '{"course_completion": {"beginner": 75, "intermediate": 150, "advanced": 250}, "course_creation": 200, "forum_post": 10, "forum_like_received": 5, "course_rating": 15}', 'Base points for different actions'),
('multipliers', '{"career_path_bonus": 1.5, "expert_validation": 1.25, "high_rating_bonus": 1.5}', 'Point multipliers for bonuses'),
('limits', '{"max_daily_forum_points": 200, "max_weekly_creation_points": 2000}', 'Daily and weekly limits for point earning');

-- Insert sample score transactions and user scores
SELECT award_points(
    '550e8400-e29b-41d4-a716-************'::UUID,
    'student',
    'course_completion',
    150,
    '550e8400-e29b-41d4-a716-************'::UUID,
    'course',
    '{"career_path_bonus": "true", "difficulty": "beginner"}'::JSONB
);

SELECT award_points(
    '550e8400-e29b-41d4-a716-************'::UUID,
    'creator',
    'course_creation',
    200,
    '550e8400-e29b-41d4-a716-************'::UUID,
    'course',
    '{"difficulty": "beginner"}'::JSONB
);

-- Generate initial leaderboard cache
SELECT generate_leaderboard_cache('student', 'weekly', DATE_TRUNC('week', CURRENT_DATE)::DATE);
SELECT generate_leaderboard_cache('student', 'monthly', DATE_TRUNC('month', CURRENT_DATE)::DATE);
SELECT generate_leaderboard_cache('student', 'alltime', '1970-01-01'::DATE);
SELECT generate_leaderboard_cache('creator', 'weekly', DATE_TRUNC('week', CURRENT_DATE)::DATE);
SELECT generate_leaderboard_cache('creator', 'monthly', DATE_TRUNC('month', CURRENT_DATE)::DATE);
SELECT generate_leaderboard_cache('creator', 'alltime', '1970-01-01'::DATE);

-- Insert sample forum categories and posts
INSERT INTO forum_categories (category_id, course_id, name, description, order_index) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'General Discussion', 'General questions and discussions', 1),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Technical Help', 'Technical questions and troubleshooting', 2);

INSERT INTO forum_posts (post_id, category_id, user_id, title, content, like_count) VALUES
('550e8400-e29b-41d4-a716-446655440060', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '¿Cuál es la diferencia entre let y var?', 'Estoy confundido sobre cuándo usar let vs var en JavaScript. ¿Alguien puede explicar?', 3),
('550e8400-e29b-41d4-a716-446655440061', '550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Error en el ejercicio 3', 'Estoy obteniendo un error "undefined is not a function" en el ejercicio 3. ¿Alguna ayuda?', 1);

-- Insert sample forum replies
INSERT INTO forum_replies (reply_id, post_id, user_id, content, like_count) VALUES
('550e8400-e29b-41d4-a716-446655440070', '550e8400-e29b-41d4-a716-446655440060', '550e8400-e29b-41d4-a716-************', 'La principal diferencia es el scope. "let" tiene block scope mientras que "var" tiene function scope...', 5);

-- Insert sample notifications
INSERT INTO notifications (user_id, tenant_id, type, title, message, status) VALUES
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440000', 'in_app', 'Curso completado', 'Has completado exitosamente el curso "JavaScript Fundamentals"', 'delivered'),
('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440000', 'in_app', 'Nuevos puntos ganados', 'Has ganado 225 puntos por completar un curso en tu Career Path activo', 'delivered');
