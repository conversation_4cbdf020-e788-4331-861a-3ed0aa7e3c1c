"""
Settings Router
Handles system settings and configuration API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status, UploadFile, File
from sqlmodel import Session
from typing import List, Optional, Dict, Any
from uuid import UUID

from ..core.database import get_session
from ..core.security import get_current_user, require_permissions
from ..models.user import User
from ..models.settings import (
    SystemSetting, SystemSettingCreate, SystemSettingUpdate,
    SystemSettingResponse, SettingsBulkUpdate
)
from ..services.settings_service import SettingsService

router = APIRouter()

# Settings
@router.get("/", response_model=List[SystemSetting])
async def get_settings(
    category: Optional[str] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["settings_view"]))
):
    """Get system settings"""
    settings_service = SettingsService(session)
    return await settings_service.get_settings(
        tenant_id=current_user.tenant_id,
        category=category,
        skip=skip,
        limit=limit
    )

@router.get("/public", response_model=List[Setting])
async def get_public_settings(
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Get public settings (non-sensitive settings that can be viewed by all users)"""
    settings_service = SettingsService(session)
    return await settings_service.get_public_settings(current_user.tenant_id)

@router.get("/{setting_key}", response_model=Setting)
async def get_setting(
    setting_key: str,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["settings_view"]))
):
    """Get a specific setting"""
    settings_service = SettingsService(session)
    setting = await settings_service.get_setting(current_user.tenant_id, setting_key)
    if not setting:
        raise HTTPException(status_code=404, detail="Setting not found")
    return setting

@router.post("/", response_model=Setting, status_code=status.HTTP_201_CREATED)
async def create_setting(
    setting_data: SettingCreate,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["settings_manage"]))
):
    """Create a new setting"""
    settings_service = SettingsService(session)
    return await settings_service.create_setting(setting_data, current_user.tenant_id)

@router.put("/{setting_key}", response_model=Setting)
async def update_setting(
    setting_key: str,
    setting_data: SettingUpdate,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["settings_manage"]))
):
    """Update a setting"""
    settings_service = SettingsService(session)
    setting = await settings_service.update_setting(
        tenant_id=current_user.tenant_id,
        key=setting_key,
        setting_data=setting_data
    )
    if not setting:
        raise HTTPException(status_code=404, detail="Setting not found")
    return setting

@router.delete("/{setting_key}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_setting(
    setting_key: str,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["settings_manage"]))
):
    """Delete a setting"""
    settings_service = SettingsService(session)
    success = await settings_service.delete_setting(current_user.tenant_id, setting_key)
    if not success:
        raise HTTPException(status_code=404, detail="Setting not found")

# Bulk Operations
@router.post("/bulk", response_model=List[Setting])
async def bulk_update_settings(
    settings_data: List[SettingUpdate],
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["settings_manage"]))
):
    """Bulk update multiple settings"""
    settings_service = SettingsService(session)
    return await settings_service.bulk_update_settings(
        tenant_id=current_user.tenant_id,
        settings_data=settings_data
    )

@router.post("/reset", response_model=List[Setting])
async def reset_settings_to_default(
    category: Optional[str] = None,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["settings_manage"]))
):
    """Reset settings to default values"""
    settings_service = SettingsService(session)
    return await settings_service.reset_to_defaults(
        tenant_id=current_user.tenant_id,
        category=category
    )

# Import/Export
@router.post("/import", response_model=Dict[str, Any])
async def import_settings(
    file: UploadFile = File(...),
    format: str = Query("json", regex="^(json|yaml|env)$"),
    overwrite: bool = Query(False),
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["settings_manage"]))
):
    """Import settings from file"""
    settings_service = SettingsService(session)
    
    # Read file content
    content = await file.read()
    
    try:
        result = await settings_service.import_settings(
            tenant_id=current_user.tenant_id,
            content=content.decode('utf-8'),
            format=format,
            overwrite=overwrite
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Import failed: {str(e)}")

@router.get("/export/{format}")
async def export_settings(
    format: str,
    category: Optional[str] = None,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["settings_view"]))
):
    """Export settings to file"""
    if format not in ["json", "yaml", "env"]:
        raise HTTPException(status_code=400, detail="Invalid format. Use json, yaml, or env")
    
    settings_service = SettingsService(session)
    content = await settings_service.export_settings(
        tenant_id=current_user.tenant_id,
        format=format,
        category=category
    )
    
    # Return appropriate response based on format
    media_type = {
        "json": "application/json",
        "yaml": "application/x-yaml",
        "env": "text/plain"
    }[format]
    
    filename = f"settings.{format}"
    
    from fastapi.responses import Response
    return Response(
        content=content,
        media_type=media_type,
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )

# Feature Flags
@router.get("/feature-flags", response_model=List[FeatureFlag])
async def get_feature_flags(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["settings_view"]))
):
    """Get feature flags"""
    settings_service = SettingsService(session)
    return await settings_service.get_feature_flags(
        tenant_id=current_user.tenant_id,
        skip=skip,
        limit=limit
    )

@router.get("/feature-flags/active", response_model=List[FeatureFlag])
async def get_active_feature_flags(
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """Get active feature flags (available to all users)"""
    settings_service = SettingsService(session)
    return await settings_service.get_active_feature_flags(current_user.tenant_id)

@router.get("/feature-flags/{flag_key}", response_model=FeatureFlag)
async def get_feature_flag(
    flag_key: str,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["settings_view"]))
):
    """Get a specific feature flag"""
    settings_service = SettingsService(session)
    flag = await settings_service.get_feature_flag(current_user.tenant_id, flag_key)
    if not flag:
        raise HTTPException(status_code=404, detail="Feature flag not found")
    return flag

@router.post("/feature-flags", response_model=FeatureFlag, status_code=status.HTTP_201_CREATED)
async def create_feature_flag(
    flag_data: FeatureFlagCreate,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["settings_manage"]))
):
    """Create a new feature flag"""
    settings_service = SettingsService(session)
    return await settings_service.create_feature_flag(flag_data, current_user.tenant_id)

@router.put("/feature-flags/{flag_key}", response_model=FeatureFlag)
async def update_feature_flag(
    flag_key: str,
    flag_data: FeatureFlagUpdate,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["settings_manage"]))
):
    """Update a feature flag"""
    settings_service = SettingsService(session)
    flag = await settings_service.update_feature_flag(
        tenant_id=current_user.tenant_id,
        key=flag_key,
        flag_data=flag_data
    )
    if not flag:
        raise HTTPException(status_code=404, detail="Feature flag not found")
    return flag

@router.delete("/feature-flags/{flag_key}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_feature_flag(
    flag_key: str,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["settings_manage"]))
):
    """Delete a feature flag"""
    settings_service = SettingsService(session)
    success = await settings_service.delete_feature_flag(current_user.tenant_id, flag_key)
    if not success:
        raise HTTPException(status_code=404, detail="Feature flag not found")

@router.post("/feature-flags/{flag_key}/toggle", response_model=FeatureFlag)
async def toggle_feature_flag(
    flag_key: str,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["settings_manage"]))
):
    """Toggle a feature flag on/off"""
    settings_service = SettingsService(session)
    flag = await settings_service.toggle_feature_flag(current_user.tenant_id, flag_key)
    if not flag:
        raise HTTPException(status_code=404, detail="Feature flag not found")
    return flag

# Validation
@router.post("/validate", response_model=Dict[str, Any])
async def validate_settings(
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["settings_view"]))
):
    """Validate all settings"""
    settings_service = SettingsService(session)
    return await settings_service.validate_all_settings(current_user.tenant_id)

@router.post("/validate/{setting_key}", response_model=Dict[str, Any])
async def validate_setting(
    setting_key: str,
    value: Any,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["settings_view"]))
):
    """Validate a specific setting value"""
    settings_service = SettingsService(session)
    return await settings_service.validate_setting_value(
        tenant_id=current_user.tenant_id,
        key=setting_key,
        value=value
    )
