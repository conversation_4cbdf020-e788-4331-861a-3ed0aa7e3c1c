"""
Notification and email models
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID
from enum import Enum

from sqlmodel import SQLModel, Field, Column
from sqlalchemy.dialects.postgresql import JSONB

from .base import TimestampMixin, TenantMixin


class NotificationType(str, Enum):
    """Notification type enumeration"""
    COURSE_ENROLLMENT = "course_enrollment"
    COURSE_COMPLETION = "course_completion"
    EXAM_AVAILABLE = "exam_available"
    EXAM_GRADED = "exam_graded"
    FORUM_REPLY = "forum_reply"
    GROUP_INVITATION = "group_invitation"
    SYSTEM_ANNOUNCEMENT = "system_announcement"
    REMINDER = "reminder"


class NotificationStatus(str, Enum):
    """Notification status enumeration"""
    UNREAD = "unread"
    READ = "read"
    ARCHIVED = "archived"


class EmailStatus(str, Enum):
    """Email status enumeration"""
    PENDING = "pending"
    SENT = "sent"
    FAILED = "failed"
    BOUNCED = "bounced"


class NotificationBase(SQLModel):
    """Base notification model"""
    title: str = Field(max_length=255)
    message: str
    notification_type: NotificationType
    status: NotificationStatus = Field(default=NotificationStatus.UNREAD)
    priority: str = Field(default="normal", max_length=20)  # low, normal, high, urgent


class Notification(NotificationBase, TenantMixin, TimestampMixin, table=True):
    """Notification table model"""
    __tablename__ = "notifications"
    
    notification_id: UUID = Field(primary_key=True)
    user_id: UUID = Field(foreign_key="users.user_id", index=True)
    related_id: Optional[UUID] = None  # ID of related object (course, exam, etc.)
    related_type: Optional[str] = Field(default=None, max_length=50)
    action_url: Optional[str] = Field(default=None, max_length=500)
    read_at: Optional[datetime] = None
    archived_at: Optional[datetime] = None
    data: Optional[Dict[str, Any]] = Field(default_factory=dict, sa_column=Column(JSONB))


class NotificationCreate(NotificationBase):
    """Notification creation model"""
    user_id: UUID
    related_id: Optional[UUID] = None
    related_type: Optional[str] = None
    action_url: Optional[str] = None
    data: Optional[Dict[str, Any]] = None


class NotificationUpdate(SQLModel):
    """Notification update model"""
    status: Optional[NotificationStatus] = None


class NotificationResponse(NotificationBase):
    """Notification response model"""
    notification_id: UUID
    user_id: UUID
    related_id: Optional[UUID] = None
    related_type: Optional[str] = None
    action_url: Optional[str] = None
    read_at: Optional[datetime] = None
    archived_at: Optional[datetime] = None
    created_at: datetime
    data: Dict[str, Any]


class EmailTemplateBase(SQLModel):
    """Base email template model"""
    name: str = Field(max_length=255, unique=True)
    subject: str = Field(max_length=255)
    html_content: str
    text_content: Optional[str] = None
    is_active: bool = Field(default=True)
    category: str = Field(max_length=100)


class EmailTemplate(EmailTemplateBase, TenantMixin, TimestampMixin, table=True):
    """Email template table model"""
    __tablename__ = "email_templates"
    
    template_id: UUID = Field(primary_key=True)
    variables: Optional[List[str]] = Field(default_factory=list, sa_column=Column(JSONB))
    extra_data: Optional[Dict[str, Any]] = Field(default_factory=dict, sa_column=Column(JSONB))


class EmailTemplateCreate(EmailTemplateBase):
    """Email template creation model"""
    variables: Optional[List[str]] = Field(default_factory=list)


class EmailTemplateUpdate(SQLModel):
    """Email template update model"""
    name: Optional[str] = Field(default=None, max_length=255)
    subject: Optional[str] = Field(default=None, max_length=255)
    html_content: Optional[str] = None
    text_content: Optional[str] = None
    is_active: Optional[bool] = None
    category: Optional[str] = Field(default=None, max_length=100)
    variables: Optional[List[str]] = None


class EmailTemplateResponse(EmailTemplateBase):
    """Email template response model"""
    template_id: UUID
    variables: List[str]
    created_at: datetime
    updated_at: datetime


class EmailQueueBase(SQLModel):
    """Base email queue model"""
    to_email: str = Field(max_length=255)
    to_name: Optional[str] = Field(default=None, max_length=255)
    subject: str = Field(max_length=255)
    html_content: str
    text_content: Optional[str] = None
    status: EmailStatus = Field(default=EmailStatus.PENDING)
    priority: int = Field(default=5, ge=1, le=10)  # 1 = highest, 10 = lowest
    scheduled_at: Optional[datetime] = None


class EmailQueue(EmailQueueBase, TenantMixin, TimestampMixin, table=True):
    """Email queue table model"""
    __tablename__ = "email_queue"
    
    email_id: UUID = Field(primary_key=True)
    template_id: Optional[UUID] = Field(foreign_key="email_templates.template_id", default=None)
    user_id: Optional[UUID] = Field(foreign_key="users.user_id", default=None, index=True)
    sent_at: Optional[datetime] = None
    failed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    attempts: int = Field(default=0)
    max_attempts: int = Field(default=3)
    variables: Optional[Dict[str, Any]] = Field(default_factory=dict, sa_column=Column(JSONB))


class EmailQueueCreate(EmailQueueBase):
    """Email queue creation model"""
    template_id: Optional[UUID] = None
    user_id: Optional[UUID] = None
    variables: Optional[Dict[str, Any]] = None


class EmailQueueResponse(EmailQueueBase):
    """Email queue response model"""
    email_id: UUID
    template_id: Optional[UUID] = None
    user_id: Optional[UUID] = None
    sent_at: Optional[datetime] = None
    failed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    attempts: int
    max_attempts: int
    created_at: datetime
    updated_at: datetime


class EmailSend(SQLModel):
    """Email send model"""
    to_email: str = Field(max_length=255)
    to_name: Optional[str] = Field(default=None, max_length=255)
    template_name: str = Field(max_length=255)
    variables: Optional[Dict[str, Any]] = Field(default_factory=dict)
    scheduled_at: Optional[datetime] = None
    priority: int = Field(default=5, ge=1, le=10)


class BulkEmailSend(SQLModel):
    """Bulk email send model"""
    recipients: List[Dict[str, str]]  # [{"email": "...", "name": "..."}]
    template_name: str = Field(max_length=255)
    variables: Optional[Dict[str, Any]] = Field(default_factory=dict)
    scheduled_at: Optional[datetime] = None
    priority: int = Field(default=5, ge=1, le=10)


class NotificationPreferences(SQLModel, table=True):
    """Notification preferences model"""
    __tablename__ = "notification_preferences"
    
    user_id: UUID = Field(foreign_key="users.user_id", primary_key=True)
    tenant_id: UUID = Field(foreign_key="tenants.tenant_id", index=True)
    email_notifications: bool = Field(default=True)
    push_notifications: bool = Field(default=True)
    sms_notifications: bool = Field(default=False)
    digest_frequency: str = Field(default="weekly", max_length=20)  # daily, weekly, monthly, never
    notification_types: Dict[str, bool] = Field(default_factory=dict, sa_column=Column(JSONB))
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class NotificationPreferencesUpdate(SQLModel):
    """Notification preferences update model"""
    email_notifications: Optional[bool] = None
    push_notifications: Optional[bool] = None
    sms_notifications: Optional[bool] = None
    digest_frequency: Optional[str] = None
    notification_types: Optional[Dict[str, bool]] = None


class WebhookEndpoint(SQLModel, table=True):
    """Webhook endpoint model"""
    __tablename__ = "webhook_endpoints"
    
    endpoint_id: UUID = Field(primary_key=True)
    tenant_id: UUID = Field(foreign_key="tenants.tenant_id", index=True)
    name: str = Field(max_length=255)
    url: str = Field(max_length=500)
    secret: str = Field(max_length=255)
    events: List[str] = Field(sa_column=Column(JSONB))
    is_active: bool = Field(default=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class WebhookDelivery(SQLModel, table=True):
    """Webhook delivery model"""
    __tablename__ = "webhook_deliveries"
    
    delivery_id: UUID = Field(primary_key=True)
    endpoint_id: UUID = Field(foreign_key="webhook_endpoints.endpoint_id", index=True)
    tenant_id: UUID = Field(foreign_key="tenants.tenant_id", index=True)
    event_type: str = Field(max_length=100)
    payload: Dict[str, Any] = Field(sa_column=Column(JSONB))
    status: str = Field(max_length=20)  # pending, success, failed
    response_code: Optional[int] = None
    response_body: Optional[str] = None
    attempts: int = Field(default=0)
    max_attempts: int = Field(default=3)
    next_attempt_at: Optional[datetime] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    delivered_at: Optional[datetime] = None
