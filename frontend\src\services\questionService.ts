import { apiClient } from './api';
import { Question } from './examService';

export interface CreateQuestionRequest {
  type: 'multiple-choice' | 'essay' | 'true-false' | 'fill-blank' | 'multimedia';
  title: string;
  content: string;
  options?: string[];
  correctAnswer?: string | string[];
  points: number;
  timeLimit?: number;
  difficulty: 'easy' | 'medium' | 'hard';
  category: string;
  tags: string[];
  explanation?: string;
  multimedia?: {
    type: 'image' | 'audio' | 'video';
    url: string;
    description?: string;
  };
}

export interface UpdateQuestionRequest {
  title?: string;
  content?: string;
  options?: string[];
  correctAnswer?: string | string[];
  points?: number;
  timeLimit?: number;
  difficulty?: 'easy' | 'medium' | 'hard';
  category?: string;
  tags?: string[];
  explanation?: string;
  multimedia?: {
    type: 'image' | 'audio' | 'video';
    url: string;
    description?: string;
  };
}

export interface QuestionFilters {
  type?: 'multiple-choice' | 'essay' | 'true-false' | 'fill-blank' | 'multimedia';
  difficulty?: 'easy' | 'medium' | 'hard';
  category?: string;
  tags?: string[];
  createdBy?: string;
  search?: string;
}

export interface QuestionBank {
  id: string;
  name: string;
  description?: string;
  category: string;
  questionCount: number;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  isPublic: boolean;
}

export interface CreateQuestionBankRequest {
  name: string;
  description?: string;
  category: string;
  isPublic?: boolean;
}

export interface BulkImportResult {
  imported: number;
  failed: number;
  errors: string[];
  questions: Question[];
}

export interface AIGenerationRequest {
  topic: string;
  difficulty: 'easy' | 'medium' | 'hard';
  questionType: 'multiple-choice' | 'essay' | 'true-false';
  count: number;
  language: string;
  additionalInstructions?: string;
}

export const questionService = {
  // Get all questions with optional filters
  async getQuestions(filters?: QuestionFilters): Promise<Question[]> {
    const params = new URLSearchParams();
    if (filters?.type) params.append('type', filters.type);
    if (filters?.difficulty) params.append('difficulty', filters.difficulty);
    if (filters?.category) params.append('category', filters.category);
    if (filters?.tags) filters.tags.forEach(tag => params.append('tags', tag));
    if (filters?.createdBy) params.append('createdBy', filters.createdBy);
    if (filters?.search) params.append('search', filters.search);

    const response = await apiClient.get(`/questions?${params.toString()}`);
    return response.data;
  },

  // Get question by ID
  async getQuestion(id: string): Promise<Question> {
    const response = await apiClient.get(`/questions/${id}`);
    return response.data;
  },

  // Create new question
  async createQuestion(data: CreateQuestionRequest): Promise<Question> {
    const response = await apiClient.post('/questions', data);
    return response.data;
  },

  // Update question
  async updateQuestion(id: string, data: UpdateQuestionRequest): Promise<Question> {
    const response = await apiClient.put(`/questions/${id}`, data);
    return response.data;
  },

  // Delete question
  async deleteQuestion(id: string): Promise<void> {
    await apiClient.delete(`/questions/${id}`);
  },

  // Duplicate question
  async duplicateQuestion(id: string): Promise<Question> {
    const response = await apiClient.post(`/questions/${id}/duplicate`);
    return response.data;
  },

  // Get question categories
  async getCategories(): Promise<string[]> {
    const response = await apiClient.get('/questions/categories');
    return response.data;
  },

  // Get question tags
  async getTags(): Promise<string[]> {
    const response = await apiClient.get('/questions/tags');
    return response.data;
  },

  // Bulk create questions
  async bulkCreateQuestions(questions: CreateQuestionRequest[]): Promise<BulkImportResult> {
    const response = await apiClient.post('/questions/bulk', { questions });
    return response.data;
  },

  // Import questions from file
  async importQuestions(file: File, format: 'csv' | 'json' | 'qti'): Promise<BulkImportResult> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('format', format);

    const response = await apiClient.post('/questions/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Export questions
  async exportQuestions(questionIds: string[], format: 'csv' | 'json' | 'qti' = 'json'): Promise<Blob> {
    const response = await apiClient.post('/questions/export', 
      { questionIds, format },
      { responseType: 'blob' }
    );
    return response.data;
  },

  // Generate questions with AI
  async generateQuestionsWithAI(request: AIGenerationRequest): Promise<Question[]> {
    const response = await apiClient.post('/questions/ai-generate', request);
    return response.data;
  },

  // Get question statistics
  async getQuestionStatistics(questionId: string): Promise<{
    totalAttempts: number;
    correctRate: number;
    averageTime: number;
    difficultyRating: number;
    usageCount: number;
  }> {
    const response = await apiClient.get(`/questions/${questionId}/statistics`);
    return response.data;
  },

  // Question Banks
  async getQuestionBanks(): Promise<QuestionBank[]> {
    const response = await apiClient.get('/question-banks');
    return response.data;
  },

  async createQuestionBank(data: CreateQuestionBankRequest): Promise<QuestionBank> {
    const response = await apiClient.post('/question-banks', data);
    return response.data;
  },

  async getQuestionBank(id: string): Promise<QuestionBank> {
    const response = await apiClient.get(`/question-banks/${id}`);
    return response.data;
  },

  async updateQuestionBank(id: string, data: Partial<CreateQuestionBankRequest>): Promise<QuestionBank> {
    const response = await apiClient.put(`/question-banks/${id}`, data);
    return response.data;
  },

  async deleteQuestionBank(id: string): Promise<void> {
    await apiClient.delete(`/question-banks/${id}`);
  },

  async getQuestionBankQuestions(bankId: string): Promise<Question[]> {
    const response = await apiClient.get(`/question-banks/${bankId}/questions`);
    return response.data;
  },

  async addQuestionToBank(bankId: string, questionId: string): Promise<void> {
    await apiClient.post(`/question-banks/${bankId}/questions`, { questionId });
  },

  async removeQuestionFromBank(bankId: string, questionId: string): Promise<void> {
    await apiClient.delete(`/question-banks/${bankId}/questions/${questionId}`);
  },

  // Search questions with advanced filters
  async searchQuestions(query: string, filters?: {
    types?: string[];
    difficulties?: string[];
    categories?: string[];
    tags?: string[];
    pointsRange?: [number, number];
    timeLimitRange?: [number, number];
  }): Promise<Question[]> {
    const response = await apiClient.post('/questions/search', {
      query,
      filters,
    });
    return response.data;
  },

  // Get question recommendations based on course content
  async getQuestionRecommendations(courseId: string, count: number = 10): Promise<Question[]> {
    const response = await apiClient.get(`/questions/recommendations?courseId=${courseId}&count=${count}`);
    return response.data;
  },

  // Validate question format
  async validateQuestion(question: CreateQuestionRequest): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const response = await apiClient.post('/questions/validate', question);
    return response.data;
  },
};
