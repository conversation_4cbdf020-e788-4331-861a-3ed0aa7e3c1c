"""
Course and content models
"""

from datetime import datetime
from typing import Optional, List
from uuid import UUID
from enum import Enum

from sqlmodel import SQLModel, Field, Column
from sqlalchemy.dialects.postgresql import JSONB

from .base import TimestampMixin, TenantMixin, MetadataMixin


class CourseStatus(str, Enum):
    """Course status enumeration"""
    DRAFT = "draft"
    PUBLISHED = "published"
    ARCHIVED = "archived"


class ContentType(str, Enum):
    """Content type enumeration"""
    TEXT = "text"
    VIDEO = "video"
    AUDIO = "audio"
    IMAGE = "image"
    PDF = "pdf"
    QUIZ = "quiz"
    ASSIGNMENT = "assignment"
    EXTERNAL_LINK = "external_link"


class CourseBase(SQLModel):
    """Base course model"""
    title: str = Field(max_length=255)
    description: Optional[str] = Field(default=None)
    short_description: Optional[str] = Field(default=None, max_length=500)
    category: str = Field(max_length=100)
    difficulty_level: str = Field(default="beginner", max_length=20)
    estimated_duration_hours: Optional[int] = Field(default=None)
    thumbnail_url: Optional[str] = Field(default=None, max_length=500)
    is_public: bool = Field(default=True)
    price: Optional[float] = Field(default=0.0)
    currency: str = Field(default="USD", max_length=3)


class Course(CourseBase, TenantMixin, TimestampMixin, table=True):
    """Course table model"""
    __tablename__ = "courses"
    
    course_id: UUID = Field(primary_key=True)
    instructor_id: UUID = Field(foreign_key="users.user_id", index=True)
    status: CourseStatus = Field(default=CourseStatus.DRAFT)
    published_at: Optional[datetime] = None
    tags: Optional[List[str]] = Field(default_factory=list, sa_column=Column(JSONB))
    learning_objectives: Optional[List[str]] = Field(default_factory=list, sa_column=Column(JSONB))
    prerequisites: Optional[List[str]] = Field(default_factory=list, sa_column=Column(JSONB))
    settings: Optional[dict] = Field(default_factory=dict, sa_column=Column(JSONB))


class CourseCreate(CourseBase):
    """Course creation model"""
    tags: Optional[List[str]] = Field(default_factory=list)
    learning_objectives: Optional[List[str]] = Field(default_factory=list)
    prerequisites: Optional[List[str]] = Field(default_factory=list)


class CourseUpdate(SQLModel):
    """Course update model"""
    title: Optional[str] = Field(default=None, max_length=255)
    description: Optional[str] = None
    short_description: Optional[str] = Field(default=None, max_length=500)
    category: Optional[str] = Field(default=None, max_length=100)
    difficulty_level: Optional[str] = Field(default=None, max_length=20)
    estimated_duration_hours: Optional[int] = None
    thumbnail_url: Optional[str] = Field(default=None, max_length=500)
    is_public: Optional[bool] = None
    price: Optional[float] = None
    status: Optional[CourseStatus] = None
    tags: Optional[List[str]] = None
    learning_objectives: Optional[List[str]] = None
    prerequisites: Optional[List[str]] = None


class CourseResponse(CourseBase):
    """Course response model"""
    course_id: UUID
    instructor_id: UUID
    status: CourseStatus
    created_at: datetime
    updated_at: datetime
    published_at: Optional[datetime] = None
    tags: List[str]
    learning_objectives: List[str]
    prerequisites: List[str]
    enrollment_count: Optional[int] = None
    completion_rate: Optional[float] = None
    average_rating: Optional[float] = None
    instructor_name: Optional[str] = None


class CourseModuleBase(SQLModel):
    """Base course module model"""
    title: str = Field(max_length=255)
    description: Optional[str] = None
    order_index: int = Field(default=0)
    is_published: bool = Field(default=False)
    estimated_duration_minutes: Optional[int] = None


class CourseModule(CourseModuleBase, TenantMixin, TimestampMixin, table=True):
    """Course module table model"""
    __tablename__ = "course_modules"
    
    module_id: UUID = Field(primary_key=True)
    course_id: UUID = Field(foreign_key="courses.course_id", index=True)


class CourseModuleCreate(CourseModuleBase):
    """Course module creation model"""
    course_id: UUID


class CourseModuleUpdate(SQLModel):
    """Course module update model"""
    title: Optional[str] = Field(default=None, max_length=255)
    description: Optional[str] = None
    order_index: Optional[int] = None
    is_published: Optional[bool] = None
    estimated_duration_minutes: Optional[int] = None


class ContentItemBase(SQLModel):
    """Base content item model"""
    title: str = Field(max_length=255)
    content_type: ContentType
    content_data: dict = Field(sa_column=Column(JSONB))
    order_index: int = Field(default=0)
    is_required: bool = Field(default=True)
    estimated_duration_minutes: Optional[int] = None


class ContentItem(ContentItemBase, TenantMixin, TimestampMixin, table=True):
    """Content item table model"""
    __tablename__ = "content_items"
    
    content_id: UUID = Field(primary_key=True)
    module_id: UUID = Field(foreign_key="course_modules.module_id", index=True)
    course_id: UUID = Field(foreign_key="courses.course_id", index=True)


class ContentItemCreate(ContentItemBase):
    """Content item creation model"""
    module_id: UUID


class ContentItemUpdate(SQLModel):
    """Content item update model"""
    title: Optional[str] = Field(default=None, max_length=255)
    content_type: Optional[ContentType] = None
    content_data: Optional[dict] = None
    order_index: Optional[int] = None
    is_required: Optional[bool] = None
    estimated_duration_minutes: Optional[int] = None


class CourseRating(SQLModel, table=True):
    """Course rating model"""
    __tablename__ = "course_ratings"
    
    rating_id: UUID = Field(primary_key=True)
    course_id: UUID = Field(foreign_key="courses.course_id", index=True)
    user_id: UUID = Field(foreign_key="users.user_id", index=True)
    tenant_id: UUID = Field(foreign_key="tenants.tenant_id", index=True)
    rating: int = Field(ge=1, le=5)
    review: Optional[str] = Field(default=None, max_length=1000)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class CourseRatingCreate(SQLModel):
    """Course rating creation model"""
    rating: int = Field(ge=1, le=5)
    review: Optional[str] = Field(default=None, max_length=1000)


class ExpertReview(SQLModel, table=True):
    """Expert review model"""
    __tablename__ = "expert_reviews"
    
    review_id: UUID = Field(primary_key=True)
    course_id: UUID = Field(foreign_key="courses.course_id", index=True)
    expert_id: UUID = Field(foreign_key="users.user_id", index=True)
    tenant_id: UUID = Field(foreign_key="tenants.tenant_id", index=True)
    rating: str = Field(max_length=20)  # 'very_bad', 'bad', 'neutral', 'good', 'excellent'
    review: str = Field(max_length=2000)
    is_highlighted: bool = Field(default=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class ExpertReviewCreate(SQLModel):
    """Expert review creation model"""
    rating: str = Field(max_length=20)
    review: str = Field(max_length=2000)
    is_highlighted: bool = Field(default=True)
