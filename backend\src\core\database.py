"""
Backend core database - compatibility import from core-api
"""

import sys
import os

# Add core-api to path
core_api_path = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'core-api')
sys.path.insert(0, core_api_path)

# Import from actual implementation
try:
    from app.core.database import *
except ImportError:
    # Fallback for testing
    def get_db():
        """Get database session."""
        pass
