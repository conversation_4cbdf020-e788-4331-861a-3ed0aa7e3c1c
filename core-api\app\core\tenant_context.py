"""
Tenant Context Management for Multi-tenant Architecture
Handles tenant isolation and context switching for RLS policies
"""

from sqlalchemy.orm import Session
from sqlalchemy import text
from contextlib import contextmanager
from typing import Optional, Dict, Any
import logging
from uuid import UUID

logger = logging.getLogger(__name__)


class TenantContext:
    """Manages tenant context for database operations with RLS"""

    def __init__(self, db: Session):
        self.db = db
        self._current_tenant_id: Optional[str] = None
        self._current_user_id: Optional[str] = None

    def set_tenant_context(self, tenant_id: str, user_id: Optional[str] = None):
        """Set the tenant context for RLS policies"""
        try:
            # Set tenant context for RLS
            self.db.execute(
                text("SELECT set_config('app.current_tenant_id', :tenant_id, false)"),
                {"tenant_id": str(tenant_id)}
            )
            self._current_tenant_id = str(tenant_id)

            if user_id:
                self.db.execute(
                    text("SELECT set_config('app.current_user_id', :user_id, false)"),
                    {"user_id": str(user_id)}
                )
                self._current_user_id = str(user_id)

            logger.debug(f"Set tenant context: tenant_id={tenant_id}, user_id={user_id}")
        except Exception as e:
            logger.error(f"Failed to set tenant context: {e}")
            raise

    def clear_tenant_context(self):
        """Clear the tenant context"""
        try:
            self.db.execute(text("SELECT set_config('app.current_tenant_id', '', false)"))
            self.db.execute(text("SELECT set_config('app.current_user_id', '', false)"))
            self._current_tenant_id = None
            self._current_user_id = None
            logger.debug("Cleared tenant context")
        except Exception as e:
            logger.error(f"Failed to clear tenant context: {e}")
            raise

    @property
    def current_tenant_id(self) -> Optional[str]:
        """Get current tenant ID"""
        return self._current_tenant_id

    @property
    def current_user_id(self) -> Optional[str]:
        """Get current user ID"""
        return self._current_user_id

    def get_tenant_context(self) -> Dict[str, Any]:
        """Get current tenant context information"""
        try:
            tenant_id = self.db.execute(
                text("SELECT current_setting('app.current_tenant_id', true)")
            ).scalar()
            user_id = self.db.execute(
                text("SELECT current_setting('app.current_user_id', true)")
            ).scalar()
            
            return {
                "tenant_id": tenant_id if tenant_id else None,
                "user_id": user_id if user_id else None
            }
        except Exception as e:
            logger.error(f"Failed to get tenant context: {e}")
            return {"tenant_id": None, "user_id": None}

    def verify_tenant_access(self, resource_tenant_id: str) -> bool:
        """Verify that current tenant can access resource"""
        if not self._current_tenant_id:
            return False
        return self._current_tenant_id == str(resource_tenant_id)


@contextmanager
def tenant_context(db: Session, tenant_id: str, user_id: Optional[str] = None):
    """Context manager for tenant operations"""
    context = TenantContext(db)
    try:
        context.set_tenant_context(tenant_id, user_id)
        yield context
    finally:
        context.clear_tenant_context()


class TenantContextManager:
    """Singleton tenant context manager for application-wide use"""
    
    _instance = None
    _contexts: Dict[str, TenantContext] = {}
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def get_context(self, session_id: str, db: Session) -> TenantContext:
        """Get or create tenant context for session"""
        if session_id not in self._contexts:
            self._contexts[session_id] = TenantContext(db)
        return self._contexts[session_id]
    
    def remove_context(self, session_id: str):
        """Remove tenant context for session"""
        if session_id in self._contexts:
            try:
                self._contexts[session_id].clear_tenant_context()
            except:
                pass
            del self._contexts[session_id]
    
    def clear_all_contexts(self):
        """Clear all tenant contexts"""
        for context in self._contexts.values():
            try:
                context.clear_tenant_context()
            except:
                pass
        self._contexts.clear()


# Global tenant context manager instance
tenant_context_manager = TenantContextManager()


def get_current_tenant_id(db: Session) -> Optional[str]:
    """Get current tenant ID from database session"""
    try:
        result = db.execute(
            text("SELECT current_setting('app.current_tenant_id', true)")
        ).scalar()
        return result if result and result != '' else None
    except Exception:
        return None


def get_current_user_id(db: Session) -> Optional[str]:
    """Get current user ID from database session"""
    try:
        result = db.execute(
            text("SELECT current_setting('app.current_user_id', true)")
        ).scalar()
        return result if result and result != '' else None
    except Exception:
        return None


def set_tenant_context_for_session(db: Session, tenant_id: str, user_id: Optional[str] = None):
    """Set tenant context for a database session"""
    context = TenantContext(db)
    context.set_tenant_context(tenant_id, user_id)
    return context


def clear_tenant_context_for_session(db: Session):
    """Clear tenant context for a database session"""
    context = TenantContext(db)
    context.clear_tenant_context()


# Utility functions for FastAPI dependency injection
def require_tenant_context(db: Session) -> str:
    """Dependency that requires tenant context to be set"""
    tenant_id = get_current_tenant_id(db)
    if not tenant_id:
        raise ValueError("Tenant context not set")
    return tenant_id


def get_optional_tenant_context(db: Session) -> Optional[str]:
    """Dependency that optionally gets tenant context"""
    return get_current_tenant_id(db)
