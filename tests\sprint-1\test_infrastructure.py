"""
Sprint 1 Infrastructure Tests
Tests for User Story #1: Development Environment Configuration

These tests validate that the development environment is properly configured
and all infrastructure components are working correctly.
"""

import pytest
import requests
import subprocess
import time
import os
from sqlalchemy import text


class TestDockerEnvironment:
    """Test Docker development environment setup."""
    
    def test_docker_compose_services_running(self, docker_services):
        """Test that all required Docker services are running."""
        # This test uses the docker_services fixture which validates services
        assert docker_services is True
    
    def test_postgres_container_accessible(self):
        """Test PostgreSQL container is accessible and responding."""
        try:
            result = subprocess.run(
                ["docker-compose", "exec", "-T", "postgres", "psql", "-U", "postgres", "-c", "SELECT 1;"],
                capture_output=True,
                text=True,
                timeout=10
            )
            assert result.returncode == 0
            assert "1 row" in result.stdout
        except subprocess.TimeoutExpired:
            pytest.fail("PostgreSQL container not responding")
    
    def test_redis_container_accessible(self):
        """Test Redis container is accessible and responding."""
        try:
            result = subprocess.run(
                ["docker-compose", "exec", "-T", "redis", "redis-cli", "set", "test", "value"],
                capture_output=True,
                text=True,
                timeout=10
            )
            assert result.returncode == 0
            
            result = subprocess.run(
                ["docker-compose", "exec", "-T", "redis", "redis-cli", "get", "test"],
                capture_output=True,
                text=True,
                timeout=10
            )
            assert result.returncode == 0
            assert "value" in result.stdout
        except subprocess.TimeoutExpired:
            pytest.fail("Redis container not responding")
    
    def test_backend_container_accessible(self):
        """Test backend container is accessible."""
        try:
            # Try to access health endpoint
            response = requests.get("http://localhost:8000/health", timeout=10)
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "healthy"
            assert "timestamp" in data
        except requests.RequestException:
            pytest.fail("Backend container not accessible")
    
    def test_docker_network_connectivity(self):
        """Test that containers can communicate with each other."""
        try:
            # Test backend can connect to PostgreSQL
            result = subprocess.run(
                ["docker-compose", "exec", "-T", "backend", "python", "-c", 
                 "import psycopg2; psycopg2.connect('*********************************************************/arroyo_university')"],
                capture_output=True,
                timeout=10
            )
            assert result.returncode == 0
        except subprocess.TimeoutExpired:
            pytest.fail("Backend cannot connect to PostgreSQL")


class TestDatabaseConnection:
    """Test database connectivity and configuration."""
    
    def test_database_connection_successful(self, test_db):
        """Test that database connection is working."""
        result = test_db.execute(text("SELECT 1 as test_value")).fetchone()
        assert result[0] == 1
    
    def test_database_tables_exist(self, test_db):
        """Test that required database tables exist."""
        # Check if tenants table exists
        result = test_db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'tenants'
            );
        """)).fetchone()
        assert result[0] is True
        
        # Check if users table exists
        result = test_db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'users'
            );
        """)).fetchone()
        assert result[0] is True
    
    def test_database_extensions_installed(self, test_db):
        """Test that required PostgreSQL extensions are installed."""
        # Check uuid-ossp extension
        result = test_db.execute(text("""
            SELECT EXISTS (
                SELECT FROM pg_extension 
                WHERE extname = 'uuid-ossp'
            );
        """)).fetchone()
        assert result[0] is True
        
        # Check pgcrypto extension
        result = test_db.execute(text("""
            SELECT EXISTS (
                SELECT FROM pg_extension 
                WHERE extname = 'pgcrypto'
            );
        """)).fetchone()
        assert result[0] is True
    
    def test_rls_functions_exist(self, test_db):
        """Test that RLS functions are properly installed."""
        # Check current_tenant_id function exists
        result = test_db.execute(text("""
            SELECT EXISTS (
                SELECT FROM pg_proc 
                WHERE proname = 'current_tenant_id'
            );
        """)).fetchone()
        assert result[0] is True
        
        # Check set_tenant_context function exists
        result = test_db.execute(text("""
            SELECT EXISTS (
                SELECT FROM pg_proc 
                WHERE proname = 'set_tenant_context'
            );
        """)).fetchone()
        assert result[0] is True


class TestAPIEndpoints:
    """Test basic API endpoint functionality."""
    
    def test_root_endpoint(self, client):
        """Test API root endpoint responds correctly."""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "ArroyoUniversity API"
        assert "version" in data
    
    def test_health_endpoint(self, client):
        """Test health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert isinstance(data["timestamp"], (int, float))
    
    def test_api_docs_accessible(self, client):
        """Test that API documentation is accessible."""
        response = client.get("/docs")
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
    
    def test_openapi_schema_accessible(self, client):
        """Test that OpenAPI schema is accessible."""
        response = client.get("/openapi.json")
        assert response.status_code == 200
        data = response.json()
        assert "openapi" in data
        assert "info" in data
        assert data["info"]["title"] == "ArroyoUniversity"


class TestEnvironmentConfiguration:
    """Test environment configuration and variables."""
    
    def test_required_environment_variables(self):
        """Test that required environment variables are set."""
        required_vars = [
            "DATABASE_URL",
            "SECRET_KEY",
            "ENVIRONMENT"
        ]
        
        for var in required_vars:
            value = os.getenv(var)
            assert value is not None, f"Environment variable {var} is not set"
            assert value.strip() != "", f"Environment variable {var} is empty"
    
    def test_database_url_format(self):
        """Test that DATABASE_URL is properly formatted."""
        db_url = os.getenv("DATABASE_URL")
        assert db_url is not None
        assert db_url.startswith("postgresql://"), "DATABASE_URL should start with postgresql://"
        assert "arroyo_university" in db_url, "DATABASE_URL should contain database name"
    
    def test_secret_key_security(self):
        """Test that SECRET_KEY meets security requirements."""
        secret_key = os.getenv("SECRET_KEY")
        assert secret_key is not None
        assert len(secret_key) >= 32, "SECRET_KEY should be at least 32 characters long"
        assert secret_key != "your-secret-key-change-in-production", "SECRET_KEY should be changed from default"


class TestDevelopmentTools:
    """Test development tools and automation."""
    
    def test_makefile_exists(self):
        """Test that Makefile exists in project root."""
        makefile_path = os.path.join(os.getcwd(), "Makefile")
        assert os.path.exists(makefile_path), "Makefile should exist in project root"
    
    def test_docker_compose_file_exists(self):
        """Test that docker-compose.yml exists."""
        compose_path = os.path.join(os.getcwd(), "docker-compose.yml")
        assert os.path.exists(compose_path), "docker-compose.yml should exist in project root"
    
    def test_requirements_file_exists(self):
        """Test that requirements.txt exists."""
        req_path = os.path.join(os.getcwd(), "backend", "requirements.txt")
        assert os.path.exists(req_path), "backend/requirements.txt should exist"
    
    def test_env_example_exists(self):
        """Test that .env example or template exists."""
        env_paths = [
            os.path.join(os.getcwd(), ".env"),
            os.path.join(os.getcwd(), ".env.example"),
            os.path.join(os.getcwd(), ".env.template")
        ]
        
        exists = any(os.path.exists(path) for path in env_paths)
        assert exists, "Environment file (.env, .env.example, or .env.template) should exist"


class TestPerformanceBaseline:
    """Test basic performance characteristics."""
    
    def test_api_response_time(self, client):
        """Test that API responses are reasonably fast."""
        start_time = time.time()
        response = client.get("/health")
        end_time = time.time()
        
        response_time = end_time - start_time
        assert response.status_code == 200
        assert response_time < 1.0, f"Health endpoint took {response_time:.2f}s, should be under 1s"
    
    def test_database_query_performance(self, test_db):
        """Test basic database query performance."""
        start_time = time.time()
        result = test_db.execute(text("SELECT COUNT(*) FROM tenants")).fetchone()
        end_time = time.time()
        
        query_time = end_time - start_time
        assert query_time < 0.1, f"Simple query took {query_time:.2f}s, should be under 0.1s"
        assert result is not None


class TestErrorHandling:
    """Test error handling and recovery."""
    
    def test_invalid_endpoint_returns_404(self, client):
        """Test that invalid endpoints return proper 404."""
        response = client.get("/nonexistent-endpoint")
        assert response.status_code == 404
    
    def test_database_error_handling(self, test_db):
        """Test that database errors are handled gracefully."""
        with pytest.raises(Exception):
            # This should raise an exception due to invalid SQL
            test_db.execute(text("SELECT * FROM nonexistent_table"))
        
        # Database session should still be usable after error
        result = test_db.execute(text("SELECT 1")).fetchone()
        assert result[0] == 1


class TestAcceptanceCriteria:
    """Test Sprint 1 User Story #1 Acceptance Criteria."""

    def test_new_developer_setup_time(self, docker_services):
        """
        Acceptance Criteria: New developer can set up environment in under 5 minutes
        This test validates the setup process is fast and automated.
        """
        # Verify all services are running (simulates successful setup)
        assert docker_services is True

        # Verify setup script exists and is executable
        setup_script = os.path.join(os.getcwd(), "scripts", "setup-dev.sh")
        if os.path.exists(setup_script):
            assert os.access(setup_script, os.X_OK), "Setup script should be executable"

    def test_environment_consistency(self, test_db, client):
        """
        Acceptance Criteria: Environment setup works consistently across different computers
        This test validates that the environment produces consistent results.
        """
        # Test database consistency
        result = test_db.execute(text("SELECT version()")).fetchone()
        assert "PostgreSQL" in result[0]

        # Test API consistency
        response = client.get("/health")
        assert response.status_code == 200
        assert response.json()["status"] == "healthy"

    def test_all_services_start_automatically(self, docker_services):
        """
        Acceptance Criteria: All required services start automatically
        This test validates that Docker Compose starts all necessary services.
        """
        assert docker_services is True

        # Verify specific services are accessible
        try:
            # PostgreSQL
            subprocess.run(
                ["docker-compose", "exec", "-T", "postgres", "pg_isready", "-U", "postgres"],
                check=True, timeout=5
            )

            # Redis
            subprocess.run(
                ["docker-compose", "exec", "-T", "redis", "redis-cli", "ping"],
                check=True, timeout=5
            )

        except (subprocess.CalledProcessError, subprocess.TimeoutExpired):
            pytest.fail("Not all services started automatically")

    def test_clear_documentation_available(self):
        """
        Acceptance Criteria: Clear documentation and help commands available
        This test validates that documentation exists and is accessible.
        """
        # Check for README files
        readme_paths = [
            os.path.join(os.getcwd(), "README.md"),
            os.path.join(os.getcwd(), "documentation", "devplan", "README.md")
        ]

        readme_exists = any(os.path.exists(path) for path in readme_paths)
        assert readme_exists, "README documentation should exist"

        # Check for Makefile help
        makefile_path = os.path.join(os.getcwd(), "Makefile")
        if os.path.exists(makefile_path):
            with open(makefile_path, 'r') as f:
                content = f.read()
                assert "help:" in content, "Makefile should have help target"

    def test_troubleshooting_capabilities(self, client):
        """
        Acceptance Criteria: Easy troubleshooting and reset capabilities
        This test validates that troubleshooting tools are available.
        """
        # Test health endpoint for troubleshooting
        response = client.get("/health")
        assert response.status_code == 200

        # Test that logs are accessible (via Docker)
        try:
            result = subprocess.run(
                ["docker-compose", "logs", "--tail=10", "backend"],
                capture_output=True, timeout=10
            )
            assert result.returncode == 0
        except subprocess.TimeoutExpired:
            pytest.fail("Cannot access service logs for troubleshooting")
