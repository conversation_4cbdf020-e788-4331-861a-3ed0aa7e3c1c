#!/bin/bash

# Demo Script for Smart Validation System
# This script demonstrates the different validation modes and their outputs

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

print_demo_header() {
    echo -e "\n${BOLD}${BLUE}========================================${NC}"
    echo -e "${BOLD}${BLUE}  $1${NC}"
    echo -e "${BOLD}${BLUE}========================================${NC}\n"
}

print_demo_section() {
    echo -e "\n${BOLD}${CYAN}--- $1 ---${NC}\n"
}

print_demo_info() {
    echo -e "${YELLOW}💡 $1${NC}"
}

print_demo_command() {
    echo -e "${MAGENTA}$ $1${NC}"
}

print_demo_header "ArroyoUniversity Smart Validation System Demo"

echo -e "${GREEN}This demo shows how the smart validation system adapts to different contexts:${NC}"
echo -e "• ${CYAN}Development${NC} - Fast, non-blocking validation"
echo -e "• ${CYAN}Startup${NC} - Comprehensive validation with helpful output"
echo -e "• ${CYAN}CI/CD${NC} - Strict validation that fails on errors"
echo -e "• ${CYAN}Production${NC} - Exhaustive pre-deployment validation"

# Demo 1: Quick Development Validation
print_demo_section "1. Quick Development Validation (5-10 seconds)"
print_demo_info "Used during daily development - fast and non-blocking"
print_demo_command "make validate"
echo ""
echo "Expected output:"
echo "🔍 Running quick environment validation..."
echo "✅ Docker services are running"
echo "✅ Database is accessible"
echo "✅ API is responding"
echo "🚀 System ready for development!"

# Demo 2: Startup Validation
print_demo_section "2. Startup Validation (15-30 seconds)"
print_demo_info "Used when starting development environment - thorough but helpful"
print_demo_command "make dev-with-tests"
echo ""
echo "Expected output:"
echo "🚀 Starting development environment with validation..."
echo "✅ Docker services are running"
echo "✅ Database is accessible"
echo "✅ API is responding"
echo "✅ API endpoints working"
echo "✅ Quick acceptance tests passed"
echo "📊 API Documentation: http://localhost:8000/docs"
echo "🏥 Health Check: http://localhost:8000/health"
echo "🚀 Ready for development!"

# Demo 3: Full Validation
print_demo_section "3. Full Validation (2-5 minutes)"
print_demo_info "Used before commits or for comprehensive testing"
print_demo_command "make test-sprint1"
echo ""
echo "Expected output:"
echo "🧪 Running Sprint 1 test suite..."
echo "✅ Infrastructure Tests: 15 passed"
echo "✅ Multi-tenant Tests: 12 passed"
echo "✅ Database RLS Tests: 18 passed"
echo "✅ Integration Tests: 8 passed"
echo "🎉 All tests passed! Sprint 1 is ready for production!"

# Demo 4: CI/CD Validation
print_demo_section "4. CI/CD Validation (5-10 minutes)"
print_demo_info "Used in automated pipelines - strict and comprehensive"
print_demo_command "make ci-test"
echo ""
echo "Expected output:"
echo "🤖 Running CI tests..."
echo "✅ Infrastructure validation complete"
echo "✅ Multi-tenant architecture validated"
echo "✅ Database security confirmed"
echo "✅ Integration tests passed"
echo "✅ Coverage: 95%"
echo "🎉 CI validation successful!"

# Demo 5: Configuration Examples
print_demo_section "5. Configuration Examples"
print_demo_info "Different ways to configure validation behavior"

echo -e "${CYAN}Development mode (fast, non-blocking):${NC}"
print_demo_command "VALIDATE_ON_START=false make dev"

echo -e "\n${CYAN}Startup mode (comprehensive, helpful):${NC}"
print_demo_command "VALIDATE_ON_START=true make dev"

echo -e "\n${CYAN}Silent mode (minimal output):${NC}"
print_demo_command "SILENT_MODE=true make validate"

echo -e "\n${CYAN}Verbose mode (detailed output):${NC}"
print_demo_command "VERBOSE_TESTS=true make test-sprint1"

echo -e "\n${CYAN}Continue on error (don't block development):${NC}"
print_demo_command "CONTINUE_ON_ERROR=true make validate"

# Demo 6: Smart Validation Modes
print_demo_section "6. Smart Validation Modes"
print_demo_info "Python-based smart validator with different modes"

echo -e "${CYAN}Quick mode:${NC}"
print_demo_command "python tests/smart_validation.py --mode quick"

echo -e "\n${CYAN}Startup mode:${NC}"
print_demo_command "python tests/smart_validation.py --mode startup"

echo -e "\n${CYAN}Full mode:${NC}"
print_demo_command "python tests/smart_validation.py --mode full --verbose"

echo -e "\n${CYAN}CI mode:${NC}"
print_demo_command "python tests/smart_validation.py --mode ci --json"

# Demo 7: Configuration Presets
print_demo_section "7. Configuration Presets"
print_demo_info "Pre-configured settings for different scenarios"

echo -e "${CYAN}Load development preset:${NC}"
print_demo_command "source tests/test_config.env && set_dev_mode && make dev"

echo -e "\n${CYAN}Load startup preset:${NC}"
print_demo_command "source tests/test_config.env && set_startup_mode && make dev-with-tests"

echo -e "\n${CYAN}Load CI preset:${NC}"
print_demo_command "source tests/test_config.env && set_ci_mode && make ci-test"

# Demo 8: Workflow Examples
print_demo_section "8. Common Workflow Examples"

echo -e "${CYAN}Morning startup:${NC}"
print_demo_command "make dev-with-tests"
print_demo_info "Starts environment with comprehensive validation and helpful output"

echo -e "\n${CYAN}During development:${NC}"
print_demo_command "make validate"
print_demo_info "Quick health check without interrupting workflow"

echo -e "\n${CYAN}Before committing:${NC}"
print_demo_command "make test-sprint1"
print_demo_info "Comprehensive test suite to ensure quality"

echo -e "\n${CYAN}Debugging issues:${NC}"
print_demo_command "make debug-health"
print_demo_info "Detailed health checks for troubleshooting"

echo -e "\n${CYAN}Fresh start:${NC}"
print_demo_command "make fresh-start"
print_demo_info "Clean rebuild with validation"

# Demo 9: Integration with Development Tools
print_demo_section "9. Integration Examples"

echo -e "${CYAN}VS Code task:${NC}"
echo '{
    "label": "Validate Sprint 1",
    "type": "shell",
    "command": "make validate",
    "group": "test"
}'

echo -e "\n${CYAN}Git pre-commit hook:${NC}"
echo '#!/bin/bash
SILENT_MODE=true CONTINUE_ON_ERROR=false ./tests/validate_sprint1.sh'

echo -e "\n${CYAN}GitHub Actions:${NC}"
echo 'steps:
  - name: Validate Environment
    run: |
      source tests/test_config.env
      set_ci_mode
      make ci-validate'

# Demo 10: Benefits Summary
print_demo_section "10. Benefits Summary"

echo -e "${GREEN}✅ Context-aware validation${NC} - Different levels for different needs"
echo -e "${GREEN}✅ Non-blocking development${NC} - Fast checks don't interrupt workflow"
echo -e "${GREEN}✅ Comprehensive testing${NC} - Full validation when needed"
echo -e "${GREEN}✅ Clear feedback${NC} - Actionable recommendations for issues"
echo -e "${GREEN}✅ Easy integration${NC} - Works with existing tools and workflows"
echo -e "${GREEN}✅ Configurable behavior${NC} - Adapts to team preferences"

print_demo_header "Demo Complete!"

echo -e "${YELLOW}To try the system:${NC}"
echo -e "1. ${CYAN}make validate${NC} - Quick validation"
echo -e "2. ${CYAN}make dev-with-tests${NC} - Start with validation"
echo -e "3. ${CYAN}make test-sprint1${NC} - Full test suite"
echo -e "4. ${CYAN}make show-config${NC} - See current configuration"

echo -e "\n${YELLOW}For more information:${NC}"
echo -e "• ${CYAN}tests/TESTING_GUIDE.md${NC} - Comprehensive guide"
echo -e "• ${CYAN}make help${NC} - All available commands"
echo -e "• ${CYAN}tests/test_config.env${NC} - Configuration options"

echo -e "\n${GREEN}Happy coding! 🚀${NC}"
