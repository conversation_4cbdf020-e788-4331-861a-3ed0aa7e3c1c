#!/usr/bin/env python3
"""
Smart Validation for Development Workflow
Intelligent test runner that adapts to different scenarios and provides
appropriate feedback for development, CI/CD, and deployment contexts.
"""

import subprocess
import sys
import time
import os
import json
from pathlib import Path
from enum import Enum
from typing import Dict, List, Optional


class ValidationMode(Enum):
    """Different validation modes for different contexts."""
    QUICK = "quick"           # Fast checks for development
    STARTUP = "startup"       # Validation during startup
    FULL = "full"            # Complete test suite
    CI = "ci"                # CI/CD pipeline validation
    DEPLOYMENT = "deployment" # Pre-deployment validation


class ValidationResult:
    """Result of a validation check."""
    def __init__(self, name: str, passed: bool, duration: float, message: str = "", details: str = ""):
        self.name = name
        self.passed = passed
        self.duration = duration
        self.message = message
        self.details = details


class SmartValidator:
    """Smart validation system that adapts to context."""
    
    def __init__(self, mode: ValidationMode = ValidationMode.QUICK, verbose: bool = False):
        self.mode = mode
        self.verbose = verbose
        self.results: List[ValidationResult] = []
        self.start_time = time.time()
        
    def log(self, message: str, level: str = "info"):
        """Log message with appropriate level."""
        if level == "error":
            print(f"❌ {message}")
        elif level == "warning":
            print(f"⚠️  {message}")
        elif level == "success":
            print(f"✅ {message}")
        elif self.verbose or level == "info":
            print(f"ℹ️  {message}")
    
    def run_check(self, name: str, check_func, critical: bool = True) -> ValidationResult:
        """Run a validation check and record results."""
        start_time = time.time()
        
        try:
            if self.verbose:
                self.log(f"Running {name}...")
            
            result = check_func()
            duration = time.time() - start_time
            
            if result.get("passed", False):
                validation_result = ValidationResult(
                    name=name,
                    passed=True,
                    duration=duration,
                    message=result.get("message", f"{name} passed"),
                    details=result.get("details", "")
                )
                if self.verbose:
                    self.log(f"{name} passed ({duration:.1f}s)", "success")
            else:
                validation_result = ValidationResult(
                    name=name,
                    passed=False,
                    duration=duration,
                    message=result.get("message", f"{name} failed"),
                    details=result.get("details", "")
                )
                level = "error" if critical else "warning"
                self.log(f"{name} failed ({duration:.1f}s): {validation_result.message}", level)
                
        except Exception as e:
            duration = time.time() - start_time
            validation_result = ValidationResult(
                name=name,
                passed=False,
                duration=duration,
                message=f"{name} error: {str(e)}",
                details=str(e)
            )
            level = "error" if critical else "warning"
            self.log(f"{name} error ({duration:.1f}s): {str(e)}", level)
        
        self.results.append(validation_result)
        return validation_result
    
    def check_docker_services(self) -> Dict:
        """Check if Docker services are running."""
        try:
            # Check if docker-compose is available
            subprocess.run(["docker-compose", "--version"], 
                         capture_output=True, check=True, timeout=5)
            
            # Check if services are running
            result = subprocess.run(["docker-compose", "ps"], 
                                  capture_output=True, text=True, timeout=10)
            
            if "Up" in result.stdout:
                return {"passed": True, "message": "Docker services are running"}
            else:
                return {"passed": False, "message": "Docker services are not running"}
                
        except subprocess.CalledProcessError:
            return {"passed": False, "message": "Docker Compose not available"}
        except subprocess.TimeoutExpired:
            return {"passed": False, "message": "Docker Compose timeout"}
    
    def check_database_connectivity(self) -> Dict:
        """Check database connectivity."""
        try:
            result = subprocess.run([
                "docker-compose", "exec", "-T", "postgres", 
                "pg_isready", "-U", "postgres"
            ], capture_output=True, timeout=10)
            
            if result.returncode == 0:
                return {"passed": True, "message": "Database is accessible"}
            else:
                return {"passed": False, "message": "Database is not accessible"}
                
        except subprocess.TimeoutExpired:
            return {"passed": False, "message": "Database connectivity timeout"}
        except Exception as e:
            return {"passed": False, "message": f"Database check error: {str(e)}"}
    
    def check_api_health(self) -> Dict:
        """Check API health endpoint."""
        try:
            import requests
            response = requests.get("http://localhost:8000/health", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "healthy":
                    return {"passed": True, "message": "API is healthy"}
                else:
                    return {"passed": False, "message": f"API unhealthy: {data}"}
            else:
                return {"passed": False, "message": f"API returned {response.status_code}"}
                
        except ImportError:
            # Fallback to curl if requests not available
            try:
                result = subprocess.run([
                    "curl", "-f", "-s", "http://localhost:8000/health"
                ], capture_output=True, timeout=10)
                
                if result.returncode == 0:
                    return {"passed": True, "message": "API is responding"}
                else:
                    return {"passed": False, "message": "API is not responding"}
            except:
                return {"passed": False, "message": "Cannot check API (no curl/requests)"}
        except Exception as e:
            return {"passed": False, "message": f"API check error: {str(e)}"}
    
    def run_quick_tests(self) -> Dict:
        """Run quick acceptance criteria tests."""
        try:
            result = subprocess.run([
                "python", "-m", "pytest", 
                "tests/sprint-1/test_infrastructure.py::TestAcceptanceCriteria",
                "-v", "--tb=no", "-q"
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                return {"passed": True, "message": "Quick tests passed"}
            else:
                return {
                    "passed": False, 
                    "message": "Quick tests failed",
                    "details": result.stdout + result.stderr
                }
                
        except subprocess.TimeoutExpired:
            return {"passed": False, "message": "Quick tests timeout"}
        except Exception as e:
            return {"passed": False, "message": f"Quick tests error: {str(e)}"}
    
    def run_infrastructure_tests(self) -> Dict:
        """Run infrastructure tests."""
        try:
            result = subprocess.run([
                "python", "-m", "pytest", 
                "tests/sprint-1/test_infrastructure.py",
                "-v" if self.verbose else "-q"
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                return {"passed": True, "message": "Infrastructure tests passed"}
            else:
                return {
                    "passed": False, 
                    "message": "Infrastructure tests failed",
                    "details": result.stdout + result.stderr
                }
                
        except subprocess.TimeoutExpired:
            return {"passed": False, "message": "Infrastructure tests timeout"}
        except Exception as e:
            return {"passed": False, "message": f"Infrastructure tests error: {str(e)}"}
    
    def validate(self) -> bool:
        """Run validation based on mode."""
        self.log(f"Starting {self.mode.value} validation...")
        
        if self.mode == ValidationMode.QUICK:
            # Quick checks for development
            self.run_check("Docker Services", self.check_docker_services, critical=True)
            self.run_check("Database Connectivity", self.check_database_connectivity, critical=True)
            self.run_check("API Health", self.check_api_health, critical=False)
            
        elif self.mode == ValidationMode.STARTUP:
            # Startup validation
            self.run_check("Docker Services", self.check_docker_services, critical=True)
            self.run_check("Database Connectivity", self.check_database_connectivity, critical=True)
            self.run_check("API Health", self.check_api_health, critical=True)
            self.run_check("Quick Tests", self.run_quick_tests, critical=False)
            
        elif self.mode == ValidationMode.FULL:
            # Full validation
            self.run_check("Docker Services", self.check_docker_services, critical=True)
            self.run_check("Database Connectivity", self.check_database_connectivity, critical=True)
            self.run_check("API Health", self.check_api_health, critical=True)
            self.run_check("Infrastructure Tests", self.run_infrastructure_tests, critical=True)
            
        elif self.mode == ValidationMode.CI:
            # CI validation - all tests must pass
            self.run_check("Docker Services", self.check_docker_services, critical=True)
            self.run_check("Database Connectivity", self.check_database_connectivity, critical=True)
            self.run_check("API Health", self.check_api_health, critical=True)
            self.run_check("Infrastructure Tests", self.run_infrastructure_tests, critical=True)
            
        elif self.mode == ValidationMode.DEPLOYMENT:
            # Pre-deployment validation
            self.run_check("Docker Services", self.check_docker_services, critical=True)
            self.run_check("Database Connectivity", self.check_database_connectivity, critical=True)
            self.run_check("API Health", self.check_api_health, critical=True)
            self.run_check("Infrastructure Tests", self.run_infrastructure_tests, critical=True)
        
        return self.generate_report()
    
    def generate_report(self) -> bool:
        """Generate validation report."""
        total_duration = time.time() - self.start_time
        passed_checks = sum(1 for r in self.results if r.passed)
        total_checks = len(self.results)
        
        print(f"\n{'='*50}")
        print(f"VALIDATION REPORT ({self.mode.value.upper()})")
        print(f"{'='*50}")
        print(f"Checks Passed: {passed_checks}/{total_checks}")
        print(f"Total Duration: {total_duration:.1f}s")
        
        if passed_checks == total_checks:
            print("✅ All validations passed!")
            
            if self.mode == ValidationMode.STARTUP:
                print("\n🚀 System is ready for development!")
                print("📊 API Documentation: http://localhost:8000/docs")
                print("🏥 Health Check: http://localhost:8000/health")
                
            return True
        else:
            print("❌ Some validations failed!")
            
            # Show failed checks
            failed_checks = [r for r in self.results if not r.passed]
            print(f"\nFailed checks:")
            for check in failed_checks:
                print(f"  ❌ {check.name}: {check.message}")
            
            # Provide recommendations
            print(f"\n💡 Recommendations:")
            if any("Docker" in r.name for r in failed_checks):
                print("  - Run 'docker-compose up -d' to start services")
            if any("Database" in r.name for r in failed_checks):
                print("  - Check database container: 'docker-compose logs postgres'")
            if any("API" in r.name for r in failed_checks):
                print("  - Check backend container: 'docker-compose logs backend'")
            
            return False


def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Smart validation for ArroyoUniversity")
    parser.add_argument("--mode", choices=[m.value for m in ValidationMode], 
                       default="quick", help="Validation mode")
    parser.add_argument("--verbose", "-v", action="store_true", 
                       help="Verbose output")
    parser.add_argument("--json", action="store_true", 
                       help="Output results as JSON")
    
    args = parser.parse_args()
    
    validator = SmartValidator(
        mode=ValidationMode(args.mode),
        verbose=args.verbose
    )
    
    success = validator.validate()
    
    if args.json:
        # Output JSON for programmatic use
        results = {
            "success": success,
            "mode": args.mode,
            "duration": time.time() - validator.start_time,
            "checks": [
                {
                    "name": r.name,
                    "passed": r.passed,
                    "duration": r.duration,
                    "message": r.message
                }
                for r in validator.results
            ]
        }
        print(json.dumps(results, indent=2))
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
