"""
Backend models user - compatibility import from core-api
"""

import sys
import os

# Add core-api to path
core_api_path = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'core-api')
sys.path.insert(0, core_api_path)

# Import from actual implementation
try:
    from app.models.user import *
except ImportError:
    # Fallback for testing
    from sqlalchemy import Column, String, Boolean, DateTime, Integer, ForeignKey
    from sqlalchemy.ext.declarative import declarative_base
    from sqlalchemy.dialects.postgresql import UUID, JSONB
    import uuid
    from datetime import datetime
    
    Base = declarative_base()
    
    class User(Base):
        __tablename__ = 'users'

        user_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
        tenant_id = Column(UUID(as_uuid=True), ForeignKey('tenants.tenant_id'), nullable=False)
        email = Column(String(255), unique=True, nullable=False)
        username = Column(String(100))
        password_hash = Column(String(255))
        first_name = Column(String(100))
        last_name = Column(String(100))
        status = Column(String(50), default='pending')
        email_verified = Column(Boolean, default=False)
        phone = Column(String(20))
        avatar_url = Column(String)
        language = Column(String(10), default='en')
        timezone = Column(String(50), default='UTC')
        created_at = Column(DateTime, default=datetime.utcnow)
        updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
        deleted_at = Column(DateTime)

        # Compatibility properties for tests
        @property
        def id(self):
            return self.user_id

        @id.setter
        def id(self, value):
            self.user_id = value

        @property
        def hashed_password(self):
            return self.password_hash

        @hashed_password.setter
        def hashed_password(self, value):
            self.password_hash = value

        @property
        def is_active(self):
            return self.status == 'active'

        @property
        def is_verified(self):
            return self.email_verified
