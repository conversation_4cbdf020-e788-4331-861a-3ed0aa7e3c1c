"""
Backend models user - compatibility import from core-api
"""

import sys
import os

# Add core-api to path
core_api_path = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'core-api')
sys.path.insert(0, core_api_path)

# Import from actual implementation
try:
    from app.models.user import *
except ImportError:
    # Fallback for testing
    from sqlalchemy import Column, String, Boolean, DateTime, Integer, ForeignKey
    from sqlalchemy.ext.declarative import declarative_base
    from sqlalchemy.dialects.postgresql import UUID, JSONB
    import uuid
    from datetime import datetime
    
    Base = declarative_base()
    
    class User(Base):
        __tablename__ = 'users'
        
        id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
        tenant_id = Column(UUID(as_uuid=True), ForeignKey('tenants.id'), nullable=False)
        email = Column(String(255), unique=True, nullable=False)
        username = Column(String(100), unique=True, nullable=False)
        first_name = Column(String(100))
        last_name = Column(String(100))
        hashed_password = Column(String(255))
        is_active = Column(Boolean, default=True)
        is_verified = Column(Boolean, default=False)
        role = Column(String(50), default='usuario')
        created_at = Column(DateTime, default=datetime.utcnow)
        updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
