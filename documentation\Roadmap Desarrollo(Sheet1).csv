,,,3. <PERSON><PERSON><PERSON>,"Estado actual
","Planeado? 
Mes?",Que es
Fase,Release,Sprint,3.1 Fase 1: MVP Foundation (Meses 3),,,
PI1,R1,1aestructurarruu,Sprint 1: Infraestructura Base,,,
PI1,R1,1,�Configuraci�n de entorno de desarrollo,Completado,,
PI1,R1,1,�Arquitectura multi-tenant b�sica,Completado,,
PI1,R1,1,�Base de datos PostgreSQL con RLS,En Progreso,,
PI1,R1,2,�Autenticaci�n JWT b�sica,Pendiente,,
PI1,R1,2,�CI/CD pipeline inicial,Pendiente,,
PI1,R1,2,Sprint 2: G<PERSON>i�n de Usuarios y Tenants,,,
PI1,R1,2,�CRUD de tenants y configuraci�n,En Progreso,,
PI1,R1,2,�Sistema de usuarios y roles b�sico,En Progreso,,
PI1,R1,2,�<PERSON>faz de administraci�n tenant,En Progreso,,
PI1,R1,2,�Invitaciones y usuarios temporales,Pendiente,,
PI1,R1,3,Sprint 3: Banco de Preguntas,,,
PI1,R1,3,�Modelo de datos para preguntas,En Progreso,,
PI1,R1,3,�CRUD manual de preguntas,En Progreso,,
PI1,R1,3,�Integraci�n b�sica con OpenAI,En Progreso,,
PI1,R1,3,�Generaci�n de preguntas Writing,Pendiente,,
PI1,R1,3,Sistema de etiquetado y taxonom�a,Pendiente,,"Estructura jer�rquica de categorizaci�n para preguntas que facilita b�squeda, filtrado y organizaci�n del contenido educativo."
PI1,R1,3,Validaci�n de calidad autom�tica,Pendiente,,"Mecanismos que verifican autom�ticamente la claridad, dificultad y relevancia de las preguntas generadas."
PI1,R2,4,Sprint 4-5 Servicios de IA,Pendiente,,
PI1,R2,4,�Integraci�n Azure Speech (TTS/STT),Pendiente,,
PI1,R2,4,�Generaci�n de preguntas Listening,Pendiente,,
PI1,R2,5,�Generaci�n de preguntas Speaking,Pendiente,,
PI1,R2,5,�Sistema de scoring b�sico,,,
PI1,R3,6,Sprint 6: Sistema de Ex�menes,Pendiente,,
PI1,R3,6,�CRUD de ex�menes,Pendiente,,
PI1,R3,6,�Motor de intentos de examen,Pendiente,,
PI1,R3,6,�Interfaz de presentaci�n,Pendiente,,
PI1,R3,6,�Autosave y recuperaci�n,Pendiente,,
PI1,R3,6,Detecci�n de sesgo en contenido generado,Pendiente,,"Herramientas para identificar y mitigar sesgos culturales, de g�nero o ideol�gicos en el contenido creado por IA."
PI1,R4,7,Sprint 7: Scoring y Reportes,,,
PI1,R4,7,�Scoring autom�tico MCQ,Pendiente,,
PI1,R4,7,�Scoring IA para respuestas abiertas,Pendiente,,
PI1,R4,7,�Dashboard b�sico,Pendiente,,
PI1,R4,7,�Exportaci�n de resultados,Pendiente,,
PI2,,,3.2 Fase 2: Expansi�n y Optimizaci�n (Meses 7-12),,,
PI2,R5,8,Sprint 8: Mejoras de UX y Performance,Pendiente,,
PI2,R5,8,�PWA con capacidades offline,Pendiente,,
PI2,R5,8,�Optimizaci�n de performance,Pendiente,,
PI2,R5,8,�Mejoras de accesibilidad (WCAG 2.1),Pendiente,,
PI2,R5,8,�Internacionalizaci�n (ES/EN),,,
PI2,R5,8,An�lisis de UX con heatmaps,,,Implementaci�n de herramientas para analizar patrones de interacci�n y optimizar la experiencia de usuario.
PI2,R6,9,Sprint 9-10: Integraciones y Anal�tica,Pendiente,,
PI2,R6,9,�SSO empresarial (SAML/OIDC),Pendiente,,
PI2,R6,9,�Webhooks para integraciones,Pendiente,,
PI2,R6,10,�Anal�tica avanzada y dashboards,Pendiente,,
PI2,R6,10,�API p�blica v1,,,
PI2,R7,11,Sprint 11: Seguridad y Cumplimiento,Pendiente,,"Evaluaci�n exhaustiva de vulnerabilidades por terceros para identificar riesgos de seguridad en c�digo, infraestructura y procesos."
PI2,R7,11,Auditor�a de seguridad completa,Pendiente,,Protecci�n de datos almacenados mediante cifrado AES-256 para cumplir con regulaciones y proteger informaci�n sensible de los usuarios.
PI2,R7,11,Implementaci�n de cifrado de datos en reposo,Pendiente,,"Mecanismo para rastrear y gestionar permisos de usuarios sobre uso de datos, cumpliendo con regulaciones de privacidad."
PI2,R7,11,Sistema de gesti�n de consentimientos,Pendiente,,"Certificaci�n que verifica controles de seguridad, disponibilidad y confidencialidad para generar confianza institucional."
PI2,R7,11,Cumplimiento SOC 2,,,
PI2,R8,12,Sprint 12: Escalabilidad y Resiliencia,Pendiente,,"Despliegue en m�ltiples regiones geogr�ficas para mejorar latencia, disponibilidad y cumplir requisitos de residencia de datos."
PI2,R8,12,Arquitectura multi-regi�n,Pendiente,,"Procedimientos y configuraciones para restaurar servicios r�pidamente tras fallos graves, con RPO/RTO definidos."
PI2,R8,12,Sistema de recuperaci�n de desastres:,Pendiente,,An�lisis y ajuste de recursos cloud para maximizar eficiencia y reducir gastos operativos.
PI2,R8,12,Optimizaci�n de costos de infraestructura:,Pendiente,,Sistema proactivo de detecci�n de anomal�as con alertas automatizadas para problemas de rendimiento o seguridad.
PI2,R8,12,Monitoreo avanzado y alertas,,,
PI3,,,3.3 Fase 3: Marketplace y Extensibilidad (Meses 13-24),,,
PI3,R9,back,Sprint 13: Ecosistema de Desarrollo,Pendiente,,Interfaces documentadas que permiten a terceros integrar sus soluciones con la plataforma.
PI3,R9,back,API para desarrolladores externos,Pendiente,,Herramientas y gu�as t�cnicas para facilitar el desarrollo de extensiones por terceros.
PI3,R9,back,Documentaci�n y SDK para plugins,Pendiente,,Framework para validar compatibilidad y seguridad de plugins antes de su publicaci�n.
PI3,R9,back,Herramientas de testing para plugins,Pendiente,,Mecanismo para gestionar actualizaciones y compatibilidad entre versiones de plugins y la plataforma.
PI3,R9,back,Sistema de versionado de plugins: ,,,
PI3,R10,back,Sprint 14: Plataforma Avanzada,Pendiente,,
PI3,R10,back,�Sistema de plugins WebAssembly,Pendiente,,
PI3,R10,back,�Marketplace de contenido,Pendiente,,
PI3,R10,back,�Anal�tica predictiva b�sica,Pendiente,,
PI3,R10,back,�Soporte multiidioma completo,,,
PI3,R10,back,Sprint 15: Integraci�n Avanzada,Pendiente,,
PI3,R10,back,�Sistema de plugins WebAssembly,Pendiente,,
PI3,R10,back,�Marketplace de contenido,Pendiente,,
PI3,R10,back,�Anal�tica predictiva b�sica,Pendiente,,
PI3,R10,back,�Soporte multiidioma completo,Pendiente,,Algoritmos que anticipan necesidades de aprendizaje y recomiendan contenido personalizado basado en patrones de uso.
,,back,IA predictiva avanzada,,,
