"""
Tenant models for multi-tenant architecture
"""

from datetime import datetime
from typing import Optional
from uuid import UUID

from sqlmodel import SQLModel, Field, Column
from sqlalchemy.dialects.postgresql import JSONB

from .base import TimestampMixin, MetadataMixin


class TenantBase(SQLModel):
    """Base tenant model"""
    name: str = Field(max_length=255)
    subdomain: str = Field(max_length=100, unique=True)
    description: Optional[str] = None
    status: str = Field(default="provisioning", max_length=50)
    plan: str = Field(default="basic", max_length=50)


class Tenant(TenantBase, TimestampMixin, table=True):
    """Tenant table model"""
    __tablename__ = "tenants"

    tenant_id: UUID = Field(primary_key=True)
    settings: Optional[dict] = Field(default_factory=dict, sa_column=Column(JSONB))


class TenantCreate(TenantBase):
    """Tenant creation model"""
    admin_email: str = Field(max_length=255)
    admin_name: str = Field(max_length=255)
    admin_password: str = Field(min_length=8)


class TenantUpdate(SQLModel):
    """Tenant update model"""
    name: Optional[str] = Field(default=None, max_length=255)
    plan: Optional[str] = Field(default=None, max_length=50)
    is_active: Optional[bool] = None
    max_users: Optional[int] = None
    max_courses: Optional[int] = None
    max_storage_gb: Optional[int] = None
    settings: Optional[dict] = None
    billing_info: Optional[dict] = None


class TenantResponse(TenantBase):
    """Tenant response model"""
    tenant_id: UUID
    created_at: datetime
    updated_at: datetime
    user_count: Optional[int] = None
    course_count: Optional[int] = None
    storage_used_gb: Optional[float] = None


class TenantStats(SQLModel):
    """Tenant statistics model"""
    tenant_id: UUID
    total_users: int
    active_users: int
    total_courses: int
    published_courses: int
    total_enrollments: int
    completed_courses: int
    storage_used_gb: float
    last_activity: Optional[datetime] = None
