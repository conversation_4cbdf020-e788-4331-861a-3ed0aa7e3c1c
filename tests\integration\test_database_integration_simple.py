"""
Simple Database Integration Test
Tests basic database connectivity and RLS functionality
"""

import pytest
import os
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Test database configuration
TEST_DATABASE_URL = os.getenv(
    "TEST_DATABASE_URL", 
    "postgresql://postgres:postgres123@localhost:5432/arroyo_university"
)


class TestSimpleDatabaseIntegration:
    """Simple database integration tests."""

    @pytest.fixture(scope="class")
    def db_engine(self):
        """Create database engine for testing."""
        try:
            engine = create_engine(TEST_DATABASE_URL, echo=False)
            # Test connection
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            yield engine
            engine.dispose()
        except Exception as e:
            pytest.skip(f"Database not available: {e}")

    @pytest.fixture
    def db_session(self, db_engine):
        """Create database session for testing."""
        Session = sessionmaker(bind=db_engine)
        session = Session()
        try:
            yield session
        finally:
            session.rollback()
            session.close()

    def test_database_connection(self, db_session):
        """Test basic database connectivity."""
        result = db_session.execute(text("SELECT 1")).scalar()
        assert result == 1

    def test_database_extensions(self, db_session):
        """Test that required PostgreSQL extensions are installed."""
        # Test uuid-ossp extension
        result = db_session.execute(text("SELECT uuid_generate_v4()")).scalar()
        assert result is not None
        
        # Test citext extension (case-insensitive text)
        result = db_session.execute(text("SELECT 'Test'::citext = 'test'::citext")).scalar()
        assert result is True

    def test_rls_helper_functions(self, db_session):
        """Test that RLS helper functions are available."""
        # Test current_tenant_id function
        db_session.execute(text("SELECT set_config('app.current_tenant_id', 'test-tenant', false)"))
        result = db_session.execute(text("SELECT current_tenant_id()")).scalar()
        assert result == 'test-tenant'
        
        # Test current_user_id function
        db_session.execute(text("SELECT set_config('app.current_user_id', 'test-user', false)"))
        result = db_session.execute(text("SELECT current_user_id()")).scalar()
        assert result == 'test-user'

    def test_tenant_table_exists(self, db_session):
        """Test that tenant table exists with correct structure."""
        # Check table exists
        result = db_session.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'tenants'
            )
        """)).scalar()
        assert result is True
        
        # Check required columns exist
        columns = db_session.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'tenants'
        """)).fetchall()
        
        column_names = [col[0] for col in columns]
        required_columns = ['tenant_id', 'name', 'subdomain', 'status']
        
        for col in required_columns:
            assert col in column_names, f"Column {col} missing from tenants table"

    def test_users_table_exists(self, db_session):
        """Test that users table exists with correct structure."""
        # Check table exists
        result = db_session.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'users'
            )
        """)).scalar()
        assert result is True
        
        # Check required columns exist
        columns = db_session.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'users'
        """)).fetchall()
        
        column_names = [col[0] for col in columns]
        required_columns = ['user_id', 'tenant_id', 'email']
        
        for col in required_columns:
            assert col in column_names, f"Column {col} missing from users table"

    def test_rls_enabled(self, db_session):
        """Test that RLS is enabled on key tables."""
        # Check RLS is enabled on users table
        result = db_session.execute(text("""
            SELECT relrowsecurity 
            FROM pg_class 
            WHERE relname = 'users'
        """)).scalar()
        assert result is True, "RLS not enabled on users table"

    def test_rls_policies_exist(self, db_session):
        """Test that RLS policies exist on key tables."""
        # Check policies exist for users table
        result = db_session.execute(text("""
            SELECT COUNT(*) 
            FROM pg_policies 
            WHERE tablename = 'users'
        """)).scalar()
        assert result > 0, "No RLS policies found for users table"

    def test_tenant_context_setting(self, db_session):
        """Test setting and getting tenant context."""
        tenant_id = 'test-tenant-123'
        
        # Set tenant context
        db_session.execute(text("SELECT set_config('app.current_tenant_id', :tenant_id, false)"), 
                          {'tenant_id': tenant_id})
        
        # Verify context is set
        result = db_session.execute(text("SELECT current_tenant_id()")).scalar()
        assert result == tenant_id
        
        # Clear context
        db_session.execute(text("SELECT set_config('app.current_tenant_id', '', false)"))
        result = db_session.execute(text("SELECT current_tenant_id()")).scalar()
        assert result == '' or result is None

    def test_basic_tenant_operations(self, db_session):
        """Test basic tenant CRUD operations."""
        # Create a test tenant
        tenant_data = {
            'tenant_id': '550e8400-e29b-41d4-a716-446655440000',
            'name': 'Test University',
            'subdomain': 'test-university-db-integration',
            'description': 'A test university for database integration',
            'status': 'active',
            'plan': 'basic'
        }
        
        # Insert tenant
        db_session.execute(text("""
            INSERT INTO tenants (tenant_id, name, subdomain, description, status, plan)
            VALUES (:tenant_id, :name, :subdomain, :description, :status, :plan)
            ON CONFLICT (tenant_id) DO NOTHING
        """), tenant_data)
        db_session.commit()
        
        # Read tenant
        result = db_session.execute(text("""
            SELECT * FROM tenants WHERE tenant_id = :tenant_id
        """), {'tenant_id': tenant_data['tenant_id']}).fetchone()
        
        assert result is not None
        assert result.name == tenant_data['name']
        assert result.subdomain == tenant_data['subdomain']
        
        # Clean up
        db_session.execute(text("""
            DELETE FROM tenants WHERE tenant_id = :tenant_id
        """), {'tenant_id': tenant_data['tenant_id']})
        db_session.commit()

    def test_database_integration_complete(self, db_session):
        """Test complete database integration workflow."""
        # This test validates that all database integration components work together
        
        # 1. Test database connectivity
        assert db_session.execute(text("SELECT 1")).scalar() == 1
        
        # 2. Test extensions are available
        uuid_result = db_session.execute(text("SELECT uuid_generate_v4()")).scalar()
        assert uuid_result is not None
        
        # 3. Test RLS functions are available
        db_session.execute(text("SELECT set_config('app.current_tenant_id', 'integration-test', false)"))
        tenant_id = db_session.execute(text("SELECT current_tenant_id()")).scalar()
        assert tenant_id == 'integration-test'
        
        # 4. Test tables exist
        tables_exist = db_session.execute(text("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_name IN ('tenants', 'users', 'courses')
        """)).scalar()
        assert tables_exist >= 2  # At least tenants and users should exist
        
        # 5. Test RLS is enabled
        rls_enabled = db_session.execute(text("""
            SELECT COUNT(*) FROM pg_class 
            WHERE relname IN ('users', 'tenants') AND relrowsecurity = true
        """)).scalar()
        assert rls_enabled >= 1  # At least users table should have RLS enabled
        
        print("✅ Database integration test completed successfully!")
        print(f"   - Database connectivity: ✅")
        print(f"   - PostgreSQL extensions: ✅")
        print(f"   - RLS helper functions: ✅")
        print(f"   - Required tables: ✅")
        print(f"   - RLS policies: ✅")
