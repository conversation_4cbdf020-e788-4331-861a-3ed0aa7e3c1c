#!/bin/bash

echo "=== ArroyoUniversity Development Setup - Sprints 1-3 ==="
echo "This script will set up the complete development environment for the first 3 sprints"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
print_status "Checking prerequisites..."

command -v docker >/dev/null 2>&1 || { print_error "Docker is required but not installed. Aborting."; exit 1; }
command -v docker-compose >/dev/null 2>&1 || { print_error "Docker Compose is required but not installed. Aborting."; exit 1; }
command -v node >/dev/null 2>&1 || { print_warning "Node.js not found. Frontend development may be limited."; }
command -v python3 >/dev/null 2>&1 || { print_warning "Python 3 not found. Backend development may be limited."; }

print_status "Prerequisites check completed."

# Create project structure
print_status "Creating project structure..."

mkdir -p {frontend,backend,database,docker,scripts,docs}
mkdir -p backend/{src,tests,migrations}
mkdir -p frontend/{src,public,tests}
mkdir -p database/{migrations,seeds,schemas}
mkdir -p docker/{development,production}
mkdir -p backend/src/{api,core,models,schemas,services}
mkdir -p backend/src/api/v1/{endpoints}

print_status "Project structure created."

# Create environment files
print_status "Creating environment configuration..."

cat > .env << EOF
# Database Configuration
DATABASE_URL=postgresql://postgres:postgres_dev_password@localhost:5432/arroyo_university
TEST_DATABASE_URL=postgresql://postgres:postgres_dev_password@localhost:5432/arroyo_university_test

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Security Configuration
SECRET_KEY=your-secret-key-change-in-production-$(openssl rand -hex 32)
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Environment
ENVIRONMENT=development
DEBUG=true

# CORS Configuration
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
ALLOWED_HOSTS=["localhost", "127.0.0.1"]

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=ArroyoUniversity
VERSION=1.0.0

# OpenAI Configuration (Sprint 3)
OPENAI_API_KEY=your-openai-api-key-here

# Azure Speech Configuration (Future sprints)
AZURE_SPEECH_KEY=your-azure-speech-key-here
AZURE_SPEECH_REGION=your-azure-region-here

# Email Configuration (Sprint 2)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# File Storage (Future sprints)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_BUCKET=your-s3-bucket-name
AWS_REGION=us-east-1
EOF

print_status "Environment file created. Please update with your actual API keys."

# Create Docker Compose configuration
print_status "Creating Docker Compose configuration..."

cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: arroyo_postgres
    environment:
      POSTGRES_DB: arroyo_university
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres_dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./documentation/devplan/shared:/docker-entrypoint-initdb.d
    networks:
      - arroyo_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: arroyo_redis
    ports:
      - "6379:6379"
    networks:
      - arroyo_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: arroyo_backend
    environment:
      - DATABASE_URL=*********************************************************/arroyo_university
      - REDIS_URL=redis://redis:6379
      - ENVIRONMENT=development
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - /app/__pycache__
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - arroyo_network
    command: ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: arroyo_frontend
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - CHOKIDAR_USEPOLLING=true
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - arroyo_network

volumes:
  postgres_data:

networks:
  arroyo_network:
    driver: bridge
EOF

print_status "Docker Compose configuration created."

# Create backend requirements
print_status "Creating backend requirements..."

cat > backend/requirements.txt << 'EOF'
# Core FastAPI dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
pydantic==2.5.0
pydantic-settings==2.1.0

# Redis & Caching
redis==5.0.1
celery==5.3.4

# AI & NLP (Sprint 3)
openai==1.3.5
textstat==0.7.3
spacy==3.7.2

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
EOF

print_status "Backend requirements created."

# Create backend Dockerfile
print_status "Creating backend Dockerfile..."

cat > backend/Dockerfile.dev << 'EOF'
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    postgresql-client \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Download spaCy model
RUN python -m spacy download en_core_web_sm

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

EXPOSE 8000

CMD ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
EOF

print_status "Backend Dockerfile created."

# Create frontend package.json
print_status "Creating frontend configuration..."

cat > frontend/package.json << 'EOF'
{
  "name": "arroyo-university-frontend",
  "version": "1.0.0",
  "private": true,
  "dependencies": {
    "@testing-library/jest-dom": "^5.17.0",
    "@testing-library/react": "^13.4.0",
    "@testing-library/user-event": "^14.5.1",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.8.0",
    "react-scripts": "5.0.1",
    "axios": "^1.6.0",
    "web-vitals": "^2.1.4"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  },
  "eslintConfig": {
    "extends": [
      "react-app",
      "react-app/jest"
    ]
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  },
  "proxy": "http://backend:8000"
}
EOF

# Create frontend Dockerfile
cat > frontend/Dockerfile.dev << 'EOF'
FROM node:18-alpine

WORKDIR /app

# Copy package files first for better caching
COPY package*.json ./
RUN npm install

# Copy application code
COPY . .

EXPOSE 3000

CMD ["npm", "start"]
EOF

print_status "Frontend configuration created."

# Create Makefile
print_status "Creating Makefile..."

cat > Makefile << 'EOF'
.PHONY: help setup dev test clean build

help:
	@echo "Available commands:"
	@echo "  setup           - Set up development environment"
	@echo "  dev             - Start development servers"
	@echo "  dev-backend     - Start only backend services"
	@echo "  dev-frontend    - Start only frontend"
	@echo "  test            - Run all tests"
	@echo "  test-backend    - Run backend tests"
	@echo "  clean           - Clean up containers and volumes"
	@echo "  build           - Build all containers"
	@echo "  migrate         - Run database migrations"
	@echo "  shell-backend   - Open backend shell"
	@echo "  shell-db        - Open database shell"
	@echo "  logs            - Show logs"

setup:
	@echo "Setting up development environment..."
	@docker-compose up -d postgres redis
	@echo "Waiting for services to be ready..."
	@sleep 10
	@docker-compose exec postgres psql -U postgres -d arroyo_university -f /docker-entrypoint-initdb.d/database-migrations.sql
	@docker-compose exec postgres createdb -U postgres arroyo_university_test || true
	@echo "Setup complete!"

dev:
	@docker-compose up

dev-backend:
	@docker-compose up postgres redis backend

dev-frontend:
	@docker-compose up frontend

test:
	@docker-compose exec backend pytest -v

test-backend:
	@docker-compose exec backend pytest tests/ -v --cov=src

clean:
	@docker-compose down -v
	@docker system prune -f

build:
	@docker-compose build --no-cache

migrate:
	@docker-compose exec postgres psql -U postgres -d arroyo_university -f /docker-entrypoint-initdb.d/database-migrations.sql

shell-backend:
	@docker-compose exec backend bash

shell-db:
	@docker-compose exec postgres psql -U postgres -d arroyo_university

logs:
	@docker-compose logs -f

validate-sprint1:
	@echo "Validating Sprint 1..."
	@docker-compose exec backend pytest tests/test_infrastructure.py -v

validate-sprint2:
	@echo "Validating Sprint 2..."
	@docker-compose exec backend pytest tests/test_sprint2.py -v

validate-sprint3:
	@echo "Validating Sprint 3..."
	@docker-compose exec backend pytest tests/test_sprint3.py -v

validate-all:
	@make validate-sprint1
	@make validate-sprint2
	@make validate-sprint3
EOF

print_status "Makefile created."

# Start services
print_status "Starting Docker services..."
docker-compose up -d postgres redis

print_status "Waiting for services to be ready..."
sleep 15

# Run database setup
print_status "Setting up database..."
docker-compose exec postgres createdb -U postgres arroyo_university_test || true

print_status "=== Setup Complete! ==="
echo ""
echo "Next steps:"
echo "1. Update .env file with your actual API keys"
echo "2. Run 'make dev' to start all services"
echo "3. Visit http://localhost:8000/docs for API documentation"
echo "4. Visit http://localhost:3000 for the frontend (when implemented)"
echo ""
echo "Available commands:"
echo "- make dev          # Start all services"
echo "- make test         # Run tests"
echo "- make validate-all # Validate all sprints"
echo "- make clean        # Clean up"
echo ""
print_status "Happy coding! 🚀"
