"""
Database Integration Tests
Tests for complete database integration including RLS, tenant context, and model functionality
"""

import pytest
import uuid
from sqlalchemy import text
from sqlalchemy.orm import Session

from backend.src.models.tenant import Tenant
from backend.src.models.user import User
from backend.src.core.tenant_context import tenant_context, TenantContext
from backend.src.core.security import get_password_hash


class TestDatabaseIntegration:
    """Test complete database integration functionality."""

    def test_database_connection(self, test_db):
        """Test basic database connectivity."""
        result = test_db.execute(text("SELECT 1")).scalar()
        assert result == 1

    def test_database_extensions(self, test_db):
        """Test that required PostgreSQL extensions are installed."""
        # Test uuid-ossp extension
        result = test_db.execute(text("SELECT uuid_generate_v4()")).scalar()
        assert result is not None
        
        # Test citext extension
        result = test_db.execute(text("SELECT 'Test'::citext = 'test'::citext")).scalar()
        assert result is True

    def test_rls_helper_functions(self, test_db):
        """Test that RLS helper functions are available."""
        # Test current_tenant_id function
        test_db.execute(text("SELECT set_config('app.current_tenant_id', 'test-tenant', false)"))
        result = test_db.execute(text("SELECT current_tenant_id()")).scalar()
        assert result == 'test-tenant'
        
        # Test current_user_id function
        test_db.execute(text("SELECT set_config('app.current_user_id', 'test-user', false)"))
        result = test_db.execute(text("SELECT current_user_id()")).scalar()
        assert result == 'test-user'

    def test_tenant_table_structure(self, test_db):
        """Test that tenant table has correct structure."""
        # Check table exists
        result = test_db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'tenants'
            )
        """)).scalar()
        assert result is True
        
        # Check required columns exist
        columns = test_db.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'tenants'
        """)).fetchall()
        
        column_names = [col[0] for col in columns]
        required_columns = ['tenant_id', 'name', 'subdomain', 'status', 'plan', 'settings']
        
        for col in required_columns:
            assert col in column_names, f"Column {col} missing from tenants table"

    def test_users_table_structure(self, test_db):
        """Test that users table has correct structure."""
        # Check table exists
        result = test_db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'users'
            )
        """)).scalar()
        assert result is True
        
        # Check required columns exist
        columns = test_db.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'users'
        """)).fetchall()
        
        column_names = [col[0] for col in columns]
        required_columns = ['user_id', 'tenant_id', 'email', 'username', 'password_hash']
        
        for col in required_columns:
            assert col in column_names, f"Column {col} missing from users table"

    def test_rls_enabled(self, test_db):
        """Test that RLS is enabled on key tables."""
        # Check RLS is enabled on tenants table
        result = test_db.execute(text("""
            SELECT relrowsecurity 
            FROM pg_class 
            WHERE relname = 'tenants'
        """)).scalar()
        assert result is True, "RLS not enabled on tenants table"
        
        # Check RLS is enabled on users table
        result = test_db.execute(text("""
            SELECT relrowsecurity 
            FROM pg_class 
            WHERE relname = 'users'
        """)).scalar()
        assert result is True, "RLS not enabled on users table"

    def test_rls_policies_exist(self, test_db):
        """Test that RLS policies exist on key tables."""
        # Check policies exist for users table
        result = test_db.execute(text("""
            SELECT COUNT(*) 
            FROM pg_policies 
            WHERE tablename = 'users'
        """)).scalar()
        assert result > 0, "No RLS policies found for users table"
        
        # Check policies exist for tenants table (if any)
        result = test_db.execute(text("""
            SELECT COUNT(*) 
            FROM pg_policies 
            WHERE tablename = 'tenants'
        """)).scalar()
        # Tenants table might not have policies as it's the root table


class TestTenantContextIntegration:
    """Test tenant context management integration."""

    def test_tenant_context_creation(self, test_db):
        """Test creating and using tenant context."""
        tenant_id = str(uuid.uuid4())
        
        # Create tenant context
        context = TenantContext(test_db)
        context.set_tenant_context(tenant_id)
        
        # Verify context is set
        assert context.current_tenant_id == tenant_id
        
        # Verify in database
        result = test_db.execute(text("SELECT current_tenant_id()")).scalar()
        assert result == tenant_id
        
        # Clear context
        context.clear_tenant_context()
        assert context.current_tenant_id is None

    def test_tenant_context_manager(self, test_db):
        """Test tenant context manager."""
        tenant_id = str(uuid.uuid4())
        user_id = str(uuid.uuid4())
        
        with tenant_context(test_db, tenant_id, user_id) as ctx:
            # Verify context is set
            assert ctx.current_tenant_id == tenant_id
            assert ctx.current_user_id == user_id
            
            # Verify in database
            db_tenant_id = test_db.execute(text("SELECT current_tenant_id()")).scalar()
            db_user_id = test_db.execute(text("SELECT current_user_id()")).scalar()
            
            assert db_tenant_id == tenant_id
            assert db_user_id == user_id
        
        # Verify context is cleared after exiting
        db_tenant_id = test_db.execute(text("SELECT current_tenant_id()")).scalar()
        assert db_tenant_id == '' or db_tenant_id is None


class TestModelIntegration:
    """Test model integration with database."""

    def test_tenant_model_crud(self, test_db):
        """Test tenant model CRUD operations."""
        # Create tenant
        tenant_data = {
            'tenant_id': str(uuid.uuid4()),
            'name': 'Test University',
            'subdomain': 'test-university',
            'description': 'A test university',
            'status': 'active',
            'plan': 'basic',
            'settings': '{"theme": "blue"}'
        }
        
        test_db.execute(text("""
            INSERT INTO tenants (tenant_id, name, subdomain, description, status, plan, settings)
            VALUES (:tenant_id, :name, :subdomain, :description, :status, :plan, :settings)
        """), tenant_data)
        test_db.commit()
        
        # Read tenant
        result = test_db.execute(text("""
            SELECT * FROM tenants WHERE tenant_id = :tenant_id
        """), {'tenant_id': tenant_data['tenant_id']}).fetchone()
        
        assert result is not None
        assert result.name == tenant_data['name']
        assert result.subdomain == tenant_data['subdomain']
        
        # Update tenant
        test_db.execute(text("""
            UPDATE tenants SET name = :new_name WHERE tenant_id = :tenant_id
        """), {'new_name': 'Updated University', 'tenant_id': tenant_data['tenant_id']})
        test_db.commit()
        
        # Verify update
        result = test_db.execute(text("""
            SELECT name FROM tenants WHERE tenant_id = :tenant_id
        """), {'tenant_id': tenant_data['tenant_id']}).scalar()
        
        assert result == 'Updated University'

    def test_user_model_with_tenant_context(self, test_db):
        """Test user model operations with tenant context."""
        # Create tenant first
        tenant_id = str(uuid.uuid4())
        test_db.execute(text("""
            INSERT INTO tenants (tenant_id, name, subdomain, status)
            VALUES (:tenant_id, 'Test Tenant', 'test-tenant', 'active')
        """), {'tenant_id': tenant_id})
        test_db.commit()
        
        # Set tenant context
        with tenant_context(test_db, tenant_id) as ctx:
            # Create user
            user_data = {
                'user_id': str(uuid.uuid4()),
                'tenant_id': tenant_id,
                'email': '<EMAIL>',
                'username': 'testuser',
                'password_hash': 'hashed_password',
                'first_name': 'Test',
                'last_name': 'User',
                'status': 'active'
            }
            
            test_db.execute(text("""
                INSERT INTO users (user_id, tenant_id, email, username, password_hash, first_name, last_name, status)
                VALUES (:user_id, :tenant_id, :email, :username, :password_hash, :first_name, :last_name, :status)
            """), user_data)
            test_db.commit()
            
            # Read user (should work with RLS)
            result = test_db.execute(text("""
                SELECT * FROM users WHERE user_id = :user_id
            """), {'user_id': user_data['user_id']}).fetchone()
            
            assert result is not None
            assert result.email == user_data['email']
            assert result.tenant_id == tenant_id


class TestRLSPolicyIntegration:
    """Test RLS policy enforcement."""

    def test_tenant_isolation_enforcement(self, test_db):
        """Test that RLS policies enforce tenant isolation."""
        # Create two tenants
        tenant1_id = str(uuid.uuid4())
        tenant2_id = str(uuid.uuid4())
        
        for tenant_id, name in [(tenant1_id, 'Tenant 1'), (tenant2_id, 'Tenant 2')]:
            test_db.execute(text("""
                INSERT INTO tenants (tenant_id, name, subdomain, status)
                VALUES (:tenant_id, :name, :subdomain, 'active')
            """), {'tenant_id': tenant_id, 'name': name, 'subdomain': f'tenant-{tenant_id[:8]}'})
        test_db.commit()
        
        # Create users in each tenant
        user1_id = str(uuid.uuid4())
        user2_id = str(uuid.uuid4())
        
        for user_id, tenant_id, email in [
            (user1_id, tenant1_id, '<EMAIL>'),
            (user2_id, tenant2_id, '<EMAIL>')
        ]:
            test_db.execute(text("""
                INSERT INTO users (user_id, tenant_id, email, username, status)
                VALUES (:user_id, :tenant_id, :email, :username, 'active')
            """), {'user_id': user_id, 'tenant_id': tenant_id, 'email': email, 'username': email.split('@')[0]})
        test_db.commit()
        
        # Test tenant 1 context - should only see tenant 1 users
        with tenant_context(test_db, tenant1_id):
            users = test_db.execute(text("SELECT * FROM users")).fetchall()
            assert len(users) == 1
            assert users[0].tenant_id == tenant1_id
        
        # Test tenant 2 context - should only see tenant 2 users
        with tenant_context(test_db, tenant2_id):
            users = test_db.execute(text("SELECT * FROM users")).fetchall()
            assert len(users) == 1
            assert users[0].tenant_id == tenant2_id
