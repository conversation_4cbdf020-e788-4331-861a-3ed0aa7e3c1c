"""
Backend models tenant - compatibility import from core-api
"""

import sys
import os

# Add core-api to path
core_api_path = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'core-api')
sys.path.insert(0, core_api_path)

# Import from actual implementation
try:
    from app.models.tenant import *
except ImportError:
    # Fallback for testing
    from sqlalchemy import Column, String, Boolean, DateTime, Integer
    from sqlalchemy.ext.declarative import declarative_base
    from sqlalchemy.dialects.postgresql import UUID, JSONB
    import uuid
    from datetime import datetime
    
    Base = declarative_base()
    
    class Tenant(Base):
        __tablename__ = 'tenants'

        id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
        name = Column(String(255), nullable=False)
        slug = Column(String(100), unique=True, nullable=False)
        domain = Column(String(255))
        is_active = Column(<PERSON>olean, default=True)
        settings = Column(JSONB)
        primary_color = Column(String(7))
        secondary_color = Column(String(7))
        contact_email = Column(String(255))
        created_at = Column(DateTime, default=datetime.utcnow)
        updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

        # Map to actual database columns
        @property
        def tenant_id(self):
            return self.id

        @tenant_id.setter
        def tenant_id(self, value):
            self.id = value

        @property
        def subdomain(self):
            return self.slug

        @subdomain.setter
        def subdomain(self, value):
            self.slug = value
