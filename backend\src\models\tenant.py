"""
Backend models tenant - compatibility import from core-api
"""

import sys
import os

# Add core-api to path
core_api_path = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'core-api')
sys.path.insert(0, core_api_path)

# Import from actual implementation
try:
    from app.models.tenant import *
except ImportError:
    # Fallback for testing
    from sqlalchemy import Column, String, Boolean, DateTime, Integer
    from sqlalchemy.ext.declarative import declarative_base
    from sqlalchemy.dialects.postgresql import UUID, JSONB
    import uuid
    from datetime import datetime
    
    Base = declarative_base()
    
    class Tenant(Base):
        __tablename__ = 'tenants'

        tenant_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
        name = Column(String(255), nullable=False)
        subdomain = Column(String(100), unique=True, nullable=False)
        description = Column(String)
        status = Column(String(50), default='provisioning')
        plan = Column(String(50), default='basic')
        settings = Column(JSONB)
        created_at = Column(DateTime, default=datetime.utcnow)
        updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
        deleted_at = Column(DateTime)

        # Compatibility properties for tests
        @property
        def id(self):
            return self.tenant_id

        @id.setter
        def id(self, value):
            self.tenant_id = value

        @property
        def slug(self):
            return self.subdomain

        @slug.setter
        def slug(self, value):
            self.subdomain = value
