"""
Sprint 1 Test Fixtures and Configuration
Provides shared test fixtures for Sprint 1 infrastructure, multi-tenant, and RLS tests.
"""

import pytest
import asyncio
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient
import os
import uuid
from datetime import datetime

# Test database configuration
TEST_DATABASE_URL = os.getenv(
    "TEST_DATABASE_URL", 
    "postgresql://postgres:postgres_dev_password@localhost:5432/arroyo_university_test"
)

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="session")
def test_engine():
    """Create test database engine for the session."""
    engine = create_engine(TEST_DATABASE_URL, echo=False)
    
    # Create test database tables
    from backend.src.models.base import Base
    Base.metadata.create_all(bind=engine)
    
    yield engine
    
    # Cleanup: Drop all tables after tests
    Base.metadata.drop_all(bind=engine)
    engine.dispose()

@pytest.fixture(scope="function")
def test_db(test_engine):
    """Create a fresh database session for each test."""
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)
    db = TestingSessionLocal()
    
    try:
        yield db
    finally:
        db.rollback()
        db.close()

@pytest.fixture(scope="function")
def client(test_db):
    """Create FastAPI test client with database override."""
    from backend.src.main import app
    from backend.src.core.database import get_db
    
    def override_get_db():
        yield test_db
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    app.dependency_overrides.clear()

@pytest.fixture
def sample_tenant(test_db):
    """Create a sample tenant for testing."""
    from backend.src.models.tenant import Tenant
    
    tenant = Tenant(
        id=str(uuid.uuid4()),
        name="Test University",
        slug="test-university",
        domain="test.example.com",
        is_active=True,
        settings={"theme": "blue", "max_users": 100},
        primary_color="#007bff",
        secondary_color="#6c757d",
        contact_email="<EMAIL>"
    )
    
    test_db.add(tenant)
    test_db.commit()
    test_db.refresh(tenant)
    
    return tenant

@pytest.fixture
def sample_tenant_2(test_db):
    """Create a second tenant for isolation testing."""
    from backend.src.models.tenant import Tenant
    
    tenant = Tenant(
        id=str(uuid.uuid4()),
        name="Second University",
        slug="second-university", 
        domain="second.example.com",
        is_active=True,
        settings={"theme": "green", "max_users": 50},
        primary_color="#28a745",
        secondary_color="#6c757d",
        contact_email="<EMAIL>"
    )
    
    test_db.add(tenant)
    test_db.commit()
    test_db.refresh(tenant)
    
    return tenant

@pytest.fixture
def sample_user(test_db, sample_tenant):
    """Create a sample user for testing."""
    from backend.src.models.user import User
    from backend.src.core.security import get_password_hash
    
    user = User(
        id=str(uuid.uuid4()),
        tenant_id=sample_tenant.id,
        email="<EMAIL>",
        username="testuser",
        first_name="Test",
        last_name="User",
        hashed_password=get_password_hash("testpassword123"),
        is_active=True,
        is_verified=True,
        role="user"
    )
    
    test_db.add(user)
    test_db.commit()
    test_db.refresh(user)
    
    return user

@pytest.fixture
def admin_user(test_db, sample_tenant):
    """Create an admin user for testing."""
    from backend.src.models.user import User
    from backend.src.core.security import get_password_hash
    
    user = User(
        id=str(uuid.uuid4()),
        tenant_id=sample_tenant.id,
        email="<EMAIL>",
        username="adminuser",
        first_name="Admin",
        last_name="User",
        hashed_password=get_password_hash("adminpassword123"),
        is_active=True,
        is_verified=True,
        role="admin"
    )
    
    test_db.add(user)
    test_db.commit()
    test_db.refresh(user)
    
    return user

@pytest.fixture
def owner_user(test_db, sample_tenant):
    """Create an owner user for testing."""
    from backend.src.models.user import User
    from backend.src.core.security import get_password_hash
    
    user = User(
        id=str(uuid.uuid4()),
        tenant_id=sample_tenant.id,
        email="<EMAIL>",
        username="owneruser",
        first_name="Owner",
        last_name="User",
        hashed_password=get_password_hash("ownerpassword123"),
        is_active=True,
        is_verified=True,
        role="owner"
    )
    
    test_db.add(user)
    test_db.commit()
    test_db.refresh(user)
    
    return user

@pytest.fixture
def auth_headers(client, sample_user, sample_tenant):
    """Create authentication headers for API testing."""
    from backend.src.core.security import create_access_token
    
    token_data = {
        "sub": sample_user.id,
        "tenant_id": sample_tenant.id,
        "role": sample_user.role,
        "email": sample_user.email
    }
    
    access_token = create_access_token(data=token_data)
    
    return {"Authorization": f"Bearer {access_token}"}

@pytest.fixture
def admin_auth_headers(client, admin_user, sample_tenant):
    """Create admin authentication headers for API testing."""
    from backend.src.core.security import create_access_token
    
    token_data = {
        "sub": admin_user.id,
        "tenant_id": sample_tenant.id,
        "role": admin_user.role,
        "email": admin_user.email
    }
    
    access_token = create_access_token(data=token_data)
    
    return {"Authorization": f"Bearer {access_token}"}

@pytest.fixture
def clean_database(test_db):
    """Clean database before and after test."""
    # Clean before test
    test_db.execute(text("TRUNCATE TABLE users, tenants RESTART IDENTITY CASCADE"))
    test_db.commit()
    
    yield
    
    # Clean after test
    test_db.execute(text("TRUNCATE TABLE users, tenants RESTART IDENTITY CASCADE"))
    test_db.commit()

@pytest.fixture
def docker_services():
    """Verify Docker services are running."""
    import subprocess
    import time
    
    # Check if services are running
    try:
        # Check PostgreSQL
        result = subprocess.run(
            ["docker-compose", "exec", "-T", "postgres", "pg_isready", "-U", "postgres"],
            capture_output=True,
            timeout=10
        )
        assert result.returncode == 0, "PostgreSQL is not ready"
        
        # Check Redis
        result = subprocess.run(
            ["docker-compose", "exec", "-T", "redis", "redis-cli", "ping"],
            capture_output=True,
            timeout=10
        )
        assert result.returncode == 0, "Redis is not ready"
        
        return True
        
    except (subprocess.TimeoutExpired, subprocess.CalledProcessError, AssertionError) as e:
        pytest.skip(f"Docker services not available: {e}")

@pytest.fixture
def rls_context(test_db, sample_tenant):
    """Set up RLS context for testing."""
    from backend.src.core.tenant_context import tenant_context
    
    with tenant_context(test_db, sample_tenant.id) as ctx:
        yield ctx
