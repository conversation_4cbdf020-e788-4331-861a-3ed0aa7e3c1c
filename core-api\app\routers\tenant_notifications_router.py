"""
Tenant Notification Configuration Router
Handles tenant-specific notification channel configuration
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlmodel import Session
from typing import List, Optional, Dict, Any
from uuid import UUID

from ..core.database import get_session
from ..core.security import get_current_user, require_permissions
from ..models.user import User
from ..models.tenant_notification_config import (
    TenantNotificationConfig, TenantNotificationConfigCreate, TenantNotificationConfigUpdate,
    TenantNotificationRule, TenantNotificationRuleCreate, TenantNotificationRuleUpdate,
    NotificationChannelType, NotificationConfigStatus
)
from ..services.tenant_notification_service import TenantNotificationConfigService

router = APIRouter()

# Channel Templates
@router.get("/templates", response_model=Dict[str, Any])
async def get_notification_channel_templates(
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["notification_manage"]))
):
    """Get available notification channel templates and configuration guides"""
    service = TenantNotificationConfigService(session)
    return await service.get_channel_templates()

# Notification Configurations
@router.get("/configs", response_model=List[TenantNotificationConfig])
async def get_notification_configs(
    channel_type: Optional[NotificationChannelType] = None,
    is_enabled: Optional[bool] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["notification_view"]))
):
    """Get notification configurations for the current tenant"""
    service = TenantNotificationConfigService(session)
    return await service.get_configs(
        tenant_id=current_user.tenant_id,
        channel_type=channel_type,
        is_enabled=is_enabled,
        skip=skip,
        limit=limit
    )

@router.post("/configs", response_model=TenantNotificationConfig, status_code=status.HTTP_201_CREATED)
async def create_notification_config(
    config_data: TenantNotificationConfigCreate,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["notification_manage"]))
):
    """Create a new notification configuration"""
    service = TenantNotificationConfigService(session)
    return await service.create_config(current_user.tenant_id, config_data)

@router.get("/configs/{config_id}", response_model=TenantNotificationConfig)
async def get_notification_config(
    config_id: UUID,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["notification_view"]))
):
    """Get a specific notification configuration"""
    service = TenantNotificationConfigService(session)
    config = await service.get_config(current_user.tenant_id, config_id)
    if not config:
        raise HTTPException(status_code=404, detail="Notification configuration not found")
    return config

@router.put("/configs/{config_id}", response_model=TenantNotificationConfig)
async def update_notification_config(
    config_id: UUID,
    config_data: TenantNotificationConfigUpdate,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["notification_manage"]))
):
    """Update a notification configuration"""
    service = TenantNotificationConfigService(session)
    config = await service.update_config(current_user.tenant_id, config_id, config_data)
    if not config:
        raise HTTPException(status_code=404, detail="Notification configuration not found")
    return config

@router.delete("/configs/{config_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_notification_config(
    config_id: UUID,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["notification_manage"]))
):
    """Delete a notification configuration"""
    service = TenantNotificationConfigService(session)
    success = await service.delete_config(current_user.tenant_id, config_id)
    if not success:
        raise HTTPException(status_code=404, detail="Notification configuration not found")

@router.post("/configs/{config_id}/test", response_model=Dict[str, Any])
async def test_notification_config(
    config_id: UUID,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["notification_manage"]))
):
    """Test a notification configuration"""
    service = TenantNotificationConfigService(session)
    result = await service.test_config(current_user.tenant_id, config_id)
    return result

@router.post("/configs/{config_id}/toggle", response_model=TenantNotificationConfig)
async def toggle_notification_config(
    config_id: UUID,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["notification_manage"]))
):
    """Toggle a notification configuration on/off"""
    service = TenantNotificationConfigService(session)
    
    # Get current config
    config = await service.get_config(current_user.tenant_id, config_id)
    if not config:
        raise HTTPException(status_code=404, detail="Notification configuration not found")
    
    # Toggle enabled status
    update_data = TenantNotificationConfigUpdate(is_enabled=not config.is_enabled)
    updated_config = await service.update_config(current_user.tenant_id, config_id, update_data)
    
    return updated_config

# Notification Rules
@router.get("/rules", response_model=List[TenantNotificationRule])
async def get_notification_rules(
    event_type: Optional[str] = None,
    is_enabled: Optional[bool] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["notification_view"]))
):
    """Get notification rules for the current tenant"""
    # Implementation would use a notification rules service
    # For now, return empty list
    return []

@router.post("/rules", response_model=TenantNotificationRule, status_code=status.HTTP_201_CREATED)
async def create_notification_rule(
    rule_data: TenantNotificationRuleCreate,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["notification_manage"]))
):
    """Create a new notification rule"""
    # Implementation would use a notification rules service
    raise HTTPException(status_code=501, detail="Notification rules not yet implemented")

@router.get("/rules/{rule_id}", response_model=TenantNotificationRule)
async def get_notification_rule(
    rule_id: UUID,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["notification_view"]))
):
    """Get a specific notification rule"""
    # Implementation would use a notification rules service
    raise HTTPException(status_code=501, detail="Notification rules not yet implemented")

@router.put("/rules/{rule_id}", response_model=TenantNotificationRule)
async def update_notification_rule(
    rule_id: UUID,
    rule_data: TenantNotificationRuleUpdate,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["notification_manage"]))
):
    """Update a notification rule"""
    # Implementation would use a notification rules service
    raise HTTPException(status_code=501, detail="Notification rules not yet implemented")

@router.delete("/rules/{rule_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_notification_rule(
    rule_id: UUID,
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["notification_manage"]))
):
    """Delete a notification rule"""
    # Implementation would use a notification rules service
    raise HTTPException(status_code=501, detail="Notification rules not yet implemented")

# Configuration Management
@router.get("/status", response_model=Dict[str, Any])
async def get_notification_status(
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["notification_view"]))
):
    """Get overall notification status for the tenant"""
    service = TenantNotificationConfigService(session)
    configs = await service.get_configs(current_user.tenant_id)
    
    status_summary = {
        "total_configs": len(configs),
        "enabled_configs": len([c for c in configs if c.is_enabled]),
        "active_configs": len([c for c in configs if c.status == NotificationConfigStatus.ACTIVE]),
        "error_configs": len([c for c in configs if c.status == NotificationConfigStatus.ERROR]),
        "channels": {}
    }
    
    # Group by channel type
    for config in configs:
        channel = config.channel_type.value
        if channel not in status_summary["channels"]:
            status_summary["channels"][channel] = {
                "total": 0,
                "enabled": 0,
                "active": 0,
                "error": 0
            }
        
        status_summary["channels"][channel]["total"] += 1
        if config.is_enabled:
            status_summary["channels"][channel]["enabled"] += 1
        if config.status == NotificationConfigStatus.ACTIVE:
            status_summary["channels"][channel]["active"] += 1
        if config.status == NotificationConfigStatus.ERROR:
            status_summary["channels"][channel]["error"] += 1
    
    return status_summary

@router.post("/test-all", response_model=Dict[str, Any])
async def test_all_notification_configs(
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["notification_manage"]))
):
    """Test all enabled notification configurations"""
    service = TenantNotificationConfigService(session)
    configs = await service.get_configs(current_user.tenant_id, is_enabled=True)
    
    results = []
    for config in configs:
        result = await service.test_config(current_user.tenant_id, config.config_id)
        results.append({
            "config_id": config.config_id,
            "name": config.name,
            "channel_type": config.channel_type,
            "success": result["success"],
            "message": result.get("message", result.get("error"))
        })
    
    return {
        "total_tested": len(results),
        "successful": len([r for r in results if r["success"]]),
        "failed": len([r for r in results if not r["success"]]),
        "results": results
    }

@router.get("/usage-stats", response_model=Dict[str, Any])
async def get_notification_usage_stats(
    days: int = Query(30, ge=1, le=365),
    session: Session = Depends(get_session),
    current_user: User = Depends(require_permissions(["notification_view"]))
):
    """Get notification usage statistics"""
    service = TenantNotificationConfigService(session)
    configs = await service.get_configs(current_user.tenant_id)
    
    total_sent = sum(config.total_sent for config in configs)
    total_failed = sum(config.total_failed for config in configs)
    
    return {
        "period_days": days,
        "total_sent": total_sent,
        "total_failed": total_failed,
        "success_rate": (total_sent / (total_sent + total_failed)) * 100 if (total_sent + total_failed) > 0 else 0,
        "channels": {
            config.channel_type.value: {
                "sent": config.total_sent,
                "failed": config.total_failed,
                "last_used": config.last_used_at.isoformat() if config.last_used_at else None
            }
            for config in configs
        }
    }
