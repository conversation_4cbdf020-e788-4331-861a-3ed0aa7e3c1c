import api from './api';

export interface TenantBranding {
  branding_id?: string;
  tenant_id?: string;
  organization_name: string;
  display_name?: string;
  description?: string;
  website?: string;
  subdomain: string;
  custom_domain?: string;
  primary_color: string;
  secondary_color: string;
  accent_color: string;
  background_color: string;
  text_color: string;
  logo_url?: string;
  logo_light_url?: string;
  logo_dark_url?: string;
  favicon_url?: string;
  login_background_url?: string;
  font_family: string;
  border_radius: string;
  header_style: 'default' | 'minimal' | 'bold';
  sidebar_style: 'default' | 'compact' | 'expanded';
  card_style: 'default' | 'elevated' | 'flat';
  button_style: 'default' | 'rounded' | 'square';
  custom_css?: string;
  custom_js?: string;
  custom_footer?: string;
  hide_arroyo_branding: boolean;
  enable_white_label: boolean;
  is_active?: boolean;
  version?: number;
  created_at?: string;
  updated_at?: string;
}

export interface SubdomainAvailability {
  subdomain: string;
  is_available: boolean;
  suggestions?: string[];
}

export interface BrandingValidation {
  is_valid: boolean;
  errors: string[];
  warnings: string[];
  subdomain_valid: boolean;
  colors_valid: boolean;
  files_valid: boolean;
  css_valid: boolean;
  js_valid: boolean;
}

export interface FileUploadResponse {
  url: string;
  filename: string;
  size: number;
}

export const brandingApi = {
  // Get current branding configuration
  async getBranding(): Promise<TenantBranding | null> {
    try {
      const response = await api.get('/tenant/branding/');
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 404) {
        return null;
      }
      throw error;
    }
  },

  // Create branding configuration
  async createBranding(data: Omit<TenantBranding, 'branding_id' | 'tenant_id' | 'created_at' | 'updated_at'>): Promise<TenantBranding> {
    const response = await api.post('/tenant/branding/', data);
    return response.data;
  },

  // Update branding configuration
  async updateBranding(data: Partial<TenantBranding>): Promise<TenantBranding> {
    const response = await api.put('/tenant/branding/', data);
    return response.data;
  },

  // Delete branding configuration
  async deleteBranding(): Promise<void> {
    await api.delete('/tenant/branding/');
  },

  // Check subdomain availability
  async checkSubdomainAvailability(subdomain: string): Promise<SubdomainAvailability> {
    const response = await api.get(`/tenant/branding/subdomain/check/${subdomain}`);
    return response.data;
  },

  // Get subdomain suggestions
  async getSubdomainSuggestions(baseName: string, count: number = 5): Promise<string[]> {
    const response = await api.get(`/tenant/branding/subdomain/suggestions/${baseName}`, {
      params: { count }
    });
    return response.data;
  },

  // Validate branding configuration
  async validateBranding(data: any): Promise<BrandingValidation> {
    const response = await api.post('/tenant/branding/validate', data);
    return response.data;
  },

  // Get generated CSS
  async getBrandingCSS(): Promise<string> {
    const response = await api.get('/tenant/branding/css', {
      responseType: 'text'
    });
    return response.data;
  },

  // Get branding history
  async getBrandingHistory(limit: number = 10): Promise<any[]> {
    const response = await api.get('/tenant/branding/history', {
      params: { limit }
    });
    return response.data;
  },

  // Upload logo
  async uploadLogo(file: File): Promise<FileUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await api.post('/tenant/branding/upload/logo', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Upload favicon
  async uploadFavicon(file: File): Promise<FileUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await api.post('/tenant/branding/upload/favicon', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Upload background image
  async uploadBackground(file: File): Promise<FileUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await api.post('/tenant/branding/upload/background', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Get default branding configuration
  async getDefaults(): Promise<TenantBranding> {
    const response = await api.get('/tenant/branding/defaults');
    return response.data;
  },

  // Reset to default configuration
  async resetToDefaults(): Promise<TenantBranding> {
    const response = await api.post('/tenant/branding/reset');
    return response.data;
  },

  // Export branding configuration
  async exportBranding(): Promise<Blob> {
    const response = await api.get('/tenant/branding/export', {
      responseType: 'blob'
    });
    return response.data;
  },

  // Import branding configuration
  async importBranding(file: File): Promise<TenantBranding> {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await api.post('/tenant/branding/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Get preview URL
  async getPreviewUrl(): Promise<{ preview_url: string; subdomain: string }> {
    const response = await api.get('/tenant/branding/preview-url');
    return response.data;
  },
};

// Notification API
export interface NotificationConfig {
  config_id?: string;
  channel_type: 'email' | 'sms' | 'slack' | 'teams' | 'webhook' | 'push';
  name: string;
  description?: string;
  is_enabled: boolean;
  status: 'active' | 'inactive' | 'testing' | 'error';
  priority: number;
  config_data: Record<string, any>;
  rate_limit_per_hour?: number;
  rate_limit_per_day?: number;
  last_test_at?: string;
  last_test_result?: string;
  last_error?: string;
  total_sent: number;
  total_failed: number;
  created_at?: string;
}

export interface NotificationTemplate {
  name: string;
  description: string;
  required_fields: string[];
  optional_fields: string[];
  field_descriptions: Record<string, string>;
  example_config: Record<string, any>;
}

export const notificationApi = {
  // Get notification templates
  async getTemplates(): Promise<Record<string, NotificationTemplate>> {
    const response = await api.get('/tenant/notifications/templates');
    return response.data;
  },

  // Get notification configurations
  async getConfigs(params?: {
    channel_type?: string;
    is_enabled?: boolean;
    skip?: number;
    limit?: number;
  }): Promise<NotificationConfig[]> {
    const response = await api.get('/tenant/notifications/configs', { params });
    return response.data;
  },

  // Create notification configuration
  async createConfig(data: Omit<NotificationConfig, 'config_id' | 'created_at' | 'total_sent' | 'total_failed'>): Promise<NotificationConfig> {
    const response = await api.post('/tenant/notifications/configs', data);
    return response.data;
  },

  // Update notification configuration
  async updateConfig(configId: string, data: Partial<NotificationConfig>): Promise<NotificationConfig> {
    const response = await api.put(`/tenant/notifications/configs/${configId}`, data);
    return response.data;
  },

  // Delete notification configuration
  async deleteConfig(configId: string): Promise<void> {
    await api.delete(`/tenant/notifications/configs/${configId}`);
  },

  // Test notification configuration
  async testConfig(configId: string): Promise<{ success: boolean; message?: string; error?: string }> {
    const response = await api.post(`/tenant/notifications/configs/${configId}/test`);
    return response.data;
  },

  // Toggle notification configuration
  async toggleConfig(configId: string): Promise<NotificationConfig> {
    const response = await api.post(`/tenant/notifications/configs/${configId}/toggle`);
    return response.data;
  },

  // Get notification status
  async getStatus(): Promise<any> {
    const response = await api.get('/tenant/notifications/status');
    return response.data;
  },

  // Test all configurations
  async testAllConfigs(): Promise<any> {
    const response = await api.post('/tenant/notifications/test-all');
    return response.data;
  },

  // Get usage statistics
  async getUsageStats(days: number = 30): Promise<any> {
    const response = await api.get('/tenant/notifications/usage-stats', {
      params: { days }
    });
    return response.data;
  },
};
