#!/usr/bin/env python3
"""
Sprint 1 Test Runner
Comprehensive test runner for Sprint 1 validation with detailed reporting.

This script runs all Sprint 1 tests and provides detailed reporting on:
- Infrastructure setup validation
- Multi-tenant architecture testing
- Database RLS security testing
- Integration testing
- Performance validation
"""

import subprocess
import sys
import time
import os
from pathlib import Path


class Colors:
    """ANSI color codes for terminal output."""
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'


class Sprint1TestRunner:
    """Test runner for Sprint 1 validation."""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = time.time()
        self.project_root = Path(__file__).parent.parent
        
    def print_header(self, title):
        """Print formatted header."""
        print(f"\n{Colors.BOLD}{Colors.BLUE}{'='*60}{Colors.END}")
        print(f"{Colors.BOLD}{Colors.BLUE}{title.center(60)}{Colors.END}")
        print(f"{Colors.BOLD}{Colors.BLUE}{'='*60}{Colors.END}\n")
    
    def print_section(self, title):
        """Print formatted section header."""
        print(f"\n{Colors.BOLD}{Colors.CYAN}{title}{Colors.END}")
        print(f"{Colors.CYAN}{'-'*len(title)}{Colors.END}")
    
    def print_success(self, message):
        """Print success message."""
        print(f"{Colors.GREEN}✅ {message}{Colors.END}")
    
    def print_error(self, message):
        """Print error message."""
        print(f"{Colors.RED}❌ {message}{Colors.END}")
    
    def print_warning(self, message):
        """Print warning message."""
        print(f"{Colors.YELLOW}⚠️  {message}{Colors.END}")
    
    def print_info(self, message):
        """Print info message."""
        print(f"{Colors.WHITE}ℹ️  {message}{Colors.END}")
    
    def check_prerequisites(self):
        """Check that all prerequisites are met."""
        self.print_section("Checking Prerequisites")
        
        # Check if we're in the right directory
        if not (self.project_root / "tests" / "sprint-1").exists():
            self.print_error("Sprint 1 test directory not found")
            return False
        
        # Check if Docker services are running
        try:
            result = subprocess.run(
                ["docker-compose", "ps"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            if result.returncode != 0:
                self.print_warning("Docker Compose not running. Starting services...")
                self.start_docker_services()
            else:
                self.print_success("Docker Compose services are running")
        except (subprocess.TimeoutExpired, FileNotFoundError):
            self.print_error("Docker Compose not available")
            return False
        
        # Check if pytest is available
        try:
            result = subprocess.run(
                ["python", "-m", "pytest", "--version"], 
                capture_output=True, 
                text=True, 
                timeout=5
            )
            if result.returncode == 0:
                self.print_success(f"Pytest available: {result.stdout.strip()}")
            else:
                self.print_error("Pytest not available")
                return False
        except (subprocess.TimeoutExpired, FileNotFoundError):
            self.print_error("Python or pytest not available")
            return False
        
        return True
    
    def start_docker_services(self):
        """Start Docker services if not running."""
        try:
            self.print_info("Starting Docker services...")
            result = subprocess.run(
                ["docker-compose", "up", "-d"], 
                capture_output=True, 
                text=True, 
                timeout=60
            )
            if result.returncode == 0:
                self.print_success("Docker services started")
                # Wait for services to be ready
                time.sleep(10)
            else:
                self.print_error(f"Failed to start Docker services: {result.stderr}")
                return False
        except subprocess.TimeoutExpired:
            self.print_error("Timeout starting Docker services")
            return False
        
        return True
    
    def run_test_category(self, test_file, category_name, description):
        """Run a specific test category."""
        self.print_section(f"Running {category_name}")
        self.print_info(description)
        
        test_path = self.project_root / "tests" / test_file
        
        try:
            start_time = time.time()
            result = subprocess.run([
                "python", "-m", "pytest", 
                str(test_path),
                "-v",
                "--tb=short",
                "--color=yes"
            ], capture_output=True, text=True, timeout=300)
            
            end_time = time.time()
            duration = end_time - start_time
            
            if result.returncode == 0:
                self.print_success(f"{category_name} tests passed ({duration:.1f}s)")
                self.test_results[category_name] = {
                    'status': 'PASSED',
                    'duration': duration,
                    'output': result.stdout
                }
            else:
                self.print_error(f"{category_name} tests failed ({duration:.1f}s)")
                self.test_results[category_name] = {
                    'status': 'FAILED',
                    'duration': duration,
                    'output': result.stdout,
                    'error': result.stderr
                }
                print(f"\n{Colors.RED}Error Output:{Colors.END}")
                print(result.stderr)
                print(f"\n{Colors.YELLOW}Test Output:{Colors.END}")
                print(result.stdout)
            
        except subprocess.TimeoutExpired:
            self.print_error(f"{category_name} tests timed out")
            self.test_results[category_name] = {
                'status': 'TIMEOUT',
                'duration': 300,
                'error': 'Test execution timed out'
            }
        except Exception as e:
            self.print_error(f"{category_name} tests error: {str(e)}")
            self.test_results[category_name] = {
                'status': 'ERROR',
                'duration': 0,
                'error': str(e)
            }
    
    def run_all_tests(self):
        """Run all Sprint 1 tests."""
        test_categories = [
            (
                "sprint-1/test_infrastructure.py",
                "Infrastructure Tests",
                "Testing development environment, Docker setup, and basic connectivity"
            ),
            (
                "sprint-1/test_multi_tenant.py",
                "Multi-tenant Architecture Tests",
                "Testing tenant isolation, data separation, and context management"
            ),
            (
                "sprint-1/test_database_rls.py",
                "Database RLS Tests",
                "Testing Row Level Security policies and database-level isolation"
            ),
            (
                "integration/test_sprint1_integration.py",
                "Integration Tests",
                "Testing end-to-end functionality and component integration"
            )
        ]
        
        for test_file, category_name, description in test_categories:
            self.run_test_category(test_file, category_name, description)
    
    def run_coverage_report(self):
        """Generate test coverage report."""
        self.print_section("Generating Coverage Report")
        
        try:
            result = subprocess.run([
                "python", "-m", "pytest",
                "tests/sprint-1/",
                "--cov=backend/src",
                "--cov-report=html",
                "--cov-report=term-missing",
                "--quiet"
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                self.print_success("Coverage report generated")
                print(result.stdout)
            else:
                self.print_warning("Coverage report generation failed")
                print(result.stderr)
                
        except subprocess.TimeoutExpired:
            self.print_warning("Coverage report generation timed out")
        except Exception as e:
            self.print_warning(f"Coverage report error: {str(e)}")
    
    def generate_summary_report(self):
        """Generate summary report of all test results."""
        self.print_header("SPRINT 1 TEST SUMMARY REPORT")
        
        total_duration = time.time() - self.start_time
        passed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'PASSED')
        total_tests = len(self.test_results)
        
        # Overall status
        if passed_tests == total_tests:
            overall_status = f"{Colors.GREEN}✅ ALL TESTS PASSED{Colors.END}"
        elif passed_tests > 0:
            overall_status = f"{Colors.YELLOW}⚠️  PARTIAL SUCCESS{Colors.END}"
        else:
            overall_status = f"{Colors.RED}❌ ALL TESTS FAILED{Colors.END}"
        
        print(f"Overall Status: {overall_status}")
        print(f"Tests Passed: {Colors.GREEN}{passed_tests}{Colors.END}/{total_tests}")
        print(f"Total Duration: {Colors.CYAN}{total_duration:.1f}s{Colors.END}")
        
        # Detailed results
        self.print_section("Detailed Results")
        for category, result in self.test_results.items():
            status_color = Colors.GREEN if result['status'] == 'PASSED' else Colors.RED
            status_icon = "✅" if result['status'] == 'PASSED' else "❌"
            
            print(f"{status_icon} {Colors.BOLD}{category}{Colors.END}")
            print(f"   Status: {status_color}{result['status']}{Colors.END}")
            print(f"   Duration: {Colors.CYAN}{result['duration']:.1f}s{Colors.END}")
            
            if result['status'] != 'PASSED' and 'error' in result:
                print(f"   Error: {Colors.RED}{result['error'][:100]}...{Colors.END}")
        
        # User Story Validation
        self.print_section("User Story Validation")
        
        user_stories = [
            ("Development Environment Configuration", "Infrastructure Tests"),
            ("Basic Multi-tenant Architecture", "Multi-tenant Architecture Tests"),
            ("PostgreSQL Database with RLS", "Database RLS Tests")
        ]
        
        for story, test_category in user_stories:
            if test_category in self.test_results:
                status = self.test_results[test_category]['status']
                if status == 'PASSED':
                    print(f"✅ {Colors.GREEN}{story}: COMPLETE{Colors.END}")
                else:
                    print(f"❌ {Colors.RED}{story}: INCOMPLETE{Colors.END}")
            else:
                print(f"⚠️  {Colors.YELLOW}{story}: NOT TESTED{Colors.END}")
        
        # Recommendations
        self.print_section("Recommendations")
        
        if passed_tests == total_tests:
            print(f"{Colors.GREEN}🎉 Sprint 1 is ready for production!{Colors.END}")
            print(f"{Colors.GREEN}✅ All user stories are complete and validated{Colors.END}")
            print(f"{Colors.GREEN}✅ Infrastructure is properly configured{Colors.END}")
            print(f"{Colors.GREEN}✅ Multi-tenant architecture is working{Colors.END}")
            print(f"{Colors.GREEN}✅ Database security is enforced{Colors.END}")
            print(f"\n{Colors.BOLD}Next Steps:{Colors.END}")
            print("1. Proceed to Sprint 2 implementation")
            print("2. Set up production environment")
            print("3. Configure monitoring and alerting")
        else:
            print(f"{Colors.RED}🔧 Sprint 1 needs attention before proceeding{Colors.END}")
            print(f"\n{Colors.BOLD}Required Actions:{Colors.END}")
            
            for category, result in self.test_results.items():
                if result['status'] != 'PASSED':
                    print(f"- Fix issues in {category}")
            
            print(f"\n{Colors.YELLOW}After fixing issues:{Colors.END}")
            print("1. Re-run tests to validate fixes")
            print("2. Review implementation guide for missed steps")
            print("3. Check Docker environment configuration")
    
    def run(self):
        """Run complete Sprint 1 test suite."""
        self.print_header("SPRINT 1 COMPREHENSIVE TEST SUITE")
        
        # Check prerequisites
        if not self.check_prerequisites():
            self.print_error("Prerequisites not met. Exiting.")
            sys.exit(1)
        
        # Run all test categories
        self.run_all_tests()
        
        # Generate coverage report
        self.run_coverage_report()
        
        # Generate summary
        self.generate_summary_report()
        
        # Exit with appropriate code
        passed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'PASSED')
        total_tests = len(self.test_results)
        
        if passed_tests == total_tests:
            sys.exit(0)  # Success
        else:
            sys.exit(1)  # Failure


if __name__ == "__main__":
    runner = Sprint1TestRunner()
    runner.run()
