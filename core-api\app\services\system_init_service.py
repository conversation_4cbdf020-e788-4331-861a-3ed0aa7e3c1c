"""
System Initialization Service
Handles the creation of system owner and initial setup
"""

import logging
from uuid import UUID, uuid4
from sqlmodel import Session, select
from typing import Optional

from ..core.config import settings
from ..models.user import User
from ..models.tenant import Tenant
from ..models.role import Role, UserRole
from ..services.auth_service import AuthService

logger = logging.getLogger(__name__)


class SystemInitService:
    """Service for system initialization"""
    
    def __init__(self, db: Session):
        self.db = db
        self.auth_service = AuthService(db)
    
    async def initialize_system(self):
        """Initialize the system with owner account and basic setup"""
        try:
            logger.info("Starting system initialization...")
            
            # Create system tenant if it doesn't exist
            system_tenant = await self._ensure_system_tenant()
            
            # Create system roles if they don't exist
            await self._ensure_system_roles(system_tenant.tenant_id)
            
            # Create system owner if it doesn't exist
            await self._ensure_system_owner(system_tenant.tenant_id)
            
            logger.info("System initialization completed successfully")
            
        except Exception as e:
            logger.error(f"System initialization failed: {e}")
            raise
    
    async def _ensure_system_tenant(self) -> Tenant:
        """Ensure system tenant exists"""
        # Check if system tenant exists
        stmt = select(Tenant).where(Tenant.subdomain == "system")
        system_tenant = self.db.exec(stmt).first()

        if not system_tenant:
            logger.info("Creating system tenant...")
            system_tenant = Tenant(
                tenant_id=uuid4(),
                name="Arroyo University System",
                subdomain="system",
                description="System tenant for platform administration",
                status="active",
                plan="enterprise"
            )
            self.db.add(system_tenant)
            self.db.commit()
            self.db.refresh(system_tenant)
            logger.info(f"System tenant created with ID: {system_tenant.tenant_id}")

        return system_tenant
    
    async def _ensure_system_roles(self, tenant_id: UUID):
        """Ensure system roles exist"""
        roles_to_create = [
            {
                "name": "System Owner",
                "description": "System owner with full platform access",
                "permissions": {"all": True},
                "is_system_role": True
            },
            {
                "name": "Tenant Admin",
                "description": "Tenant administrator with tenant management access",
                "permissions": {
                    "tenant:manage": True,
                    "users:manage": True,
                    "roles:manage": True,
                    "courses:manage": True,
                    "groups:manage": True,
                    "settings:manage": True
                },
                "is_system_role": True
            },
            {
                "name": "Usuario",
                "description": "Regular user with basic access",
                "permissions": {
                    "courses:view": True,
                    "courses:enroll": True,
                    "profile:manage": True
                },
                "is_system_role": True
            }
        ]
        
        for role_data in roles_to_create:
            # Check if role exists
            stmt = select(Role).where(
                Role.name == role_data["name"],
                Role.tenant_id == tenant_id
            )
            existing_role = self.db.exec(stmt).first()
            
            if not existing_role:
                logger.info(f"Creating system role: {role_data['name']}")
                role = Role(
                    role_id=uuid4(),
                    tenant_id=tenant_id,
                    name=role_data["name"],
                    description=role_data["description"],
                    permissions=role_data["permissions"],
                    is_system_role=role_data["is_system_role"]
                )
                self.db.add(role)
        
        self.db.commit()
    
    async def _ensure_system_owner(self, tenant_id: UUID):
        """Ensure system owner exists"""
        # Check if system owner exists
        stmt = select(User).where(
            User.email == settings.SYSTEM_OWNER_EMAIL,
            User.tenant_id == tenant_id
        )
        system_owner = self.db.exec(stmt).first()
        
        if not system_owner:
            logger.info("Creating system owner account...")
            
            # Get the System Owner role
            stmt = select(Role).where(
                Role.name == "System Owner",
                Role.tenant_id == tenant_id
            )
            owner_role = self.db.exec(stmt).first()
            
            if not owner_role:
                raise Exception("System Owner role not found")
            
            # Create system owner user
            system_owner = User(
                user_id=uuid4(),
                tenant_id=tenant_id,
                email=settings.SYSTEM_OWNER_EMAIL,
                first_name=settings.SYSTEM_OWNER_FIRST_NAME,
                last_name=settings.SYSTEM_OWNER_LAST_NAME,
                password_hash=self.auth_service._hash_password(settings.SYSTEM_OWNER_PASSWORD),
                status="active",
                email_verified=True
            )
            
            self.db.add(system_owner)
            self.db.commit()
            self.db.refresh(system_owner)
            
            # Assign System Owner role
            user_role = UserRole(
                user_id=system_owner.user_id,
                role_id=owner_role.role_id,
                assigned_by=system_owner.user_id
            )
            self.db.add(user_role)
            self.db.commit()
            
            logger.info(f"System owner created: {settings.SYSTEM_OWNER_EMAIL}")
        else:
            logger.info("System owner already exists")
    
    def get_system_tenant_id(self) -> Optional[UUID]:
        """Get the system tenant ID"""
        stmt = select(Tenant).where(Tenant.subdomain == "system")
        system_tenant = self.db.exec(stmt).first()
        return system_tenant.tenant_id if system_tenant else None
