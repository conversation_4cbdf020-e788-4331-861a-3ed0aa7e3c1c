# Arroyo University Core API Environment Variables

# Application Settings
APP_NAME=Arroyo University Core API
APP_VERSION=1.0.0
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# Database Configuration
DATABASE_URL=***********************************************/arroyo_university
POSTGRES_DB=arroyo_university
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres123

# Redis Configuration
REDIS_URL=redis://:redis123@redis:6379/0
REDIS_PASSWORD=redis123

# Security
SECRET_KEY=your-secret-key-here-change-in-production
JWT_SECRET_KEY=your-jwt-secret-key-here-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30

# CORS Settings
CORS_ORIGINS=["http://localhost:3000", "http://localhost:80"]
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
CORS_ALLOW_HEADERS=["*"]

# MinIO Storage Configuration
MINIO_ENDPOINT=minio:9000
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=minioadmin123
MINIO_SECURE=false

# AI Services Configuration
OPENAI_API_KEY=your-openai-api-key-here
AZURE_SPEECH_KEY=your-azure-speech-key-here
AZURE_SPEECH_REGION=your-azure-region-here
TURNITIN_API_KEY=your-turnitin-api-key-here

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true
SMTP_SSL=false

# System Owner Configuration
SYSTEM_OWNER_EMAIL=<EMAIL>
SYSTEM_OWNER_PASSWORD=ChangeMe123!
SYSTEM_OWNER_FIRST_NAME=System
SYSTEM_OWNER_LAST_NAME=Owner

# Monitoring
SENTRY_DSN=your-sentry-dsn-here
PROMETHEUS_ENABLED=true

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# File Upload Limits
MAX_FILE_SIZE_MB=50
ALLOWED_FILE_TYPES=["image/jpeg", "image/png", "image/gif", "application/pdf", "audio/mpeg", "audio/wav"]

# Tenant Configuration
DEFAULT_TENANT_SUBDOMAIN=demo
TENANT_ISOLATION_ENABLED=true

# Cache Configuration
CACHE_TTL_SECONDS=3600
CACHE_ENABLED=true
