"""
Analytics and scoring models
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from uuid import UUID
from enum import Enum

from sqlmodel import SQLModel, Field, Column
from sqlalchemy.dialects.postgresql import JSONB

from .base import TimestampMixin, TenantMixin


class ScoreType(str, Enum):
    """Score type enumeration"""
    COURSE_COMPLETION = "course_completion"
    EXAM_SCORE = "exam_score"
    FORUM_PARTICIPATION = "forum_participation"
    STREAK_BONUS = "streak_bonus"
    ACHIEVEMENT = "achievement"
    MANUAL_ADJUSTMENT = "manual_adjustment"


class MetricType(str, Enum):
    """Metric type enumeration"""
    USER_REGISTRATION = "user_registration"
    COURSE_ENROLLMENT = "course_enrollment"
    COURSE_COMPLETION = "course_completion"
    EXAM_SUBMISSION = "exam_submission"
    FORUM_POST = "forum_post"
    LOGIN = "login"
    PAGE_VIEW = "page_view"


class UserScoreBase(SQLModel):
    """Base user score model"""
    total_points: int = Field(default=0)
    course_points: int = Field(default=0)
    exam_points: int = Field(default=0)
    forum_points: int = Field(default=0)
    streak_points: int = Field(default=0)
    current_streak: int = Field(default=0)
    longest_streak: int = Field(default=0)
    level: int = Field(default=1)
    rank: Optional[int] = None


class UserScore(UserScoreBase, TenantMixin, TimestampMixin, table=True):
    """User score table model"""
    __tablename__ = "user_scores"
    
    user_id: UUID = Field(foreign_key="users.user_id", primary_key=True)
    last_activity_date: Optional[datetime] = None
    achievements: Optional[List[str]] = Field(default_factory=list, sa_column=Column(JSONB))
    statistics: Optional[Dict[str, Any]] = Field(default_factory=dict, sa_column=Column(JSONB))


class UserScoreResponse(UserScoreBase):
    """User score response model"""
    user_id: UUID
    last_activity_date: Optional[datetime] = None
    achievements: List[str]
    statistics: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    user_name: Optional[str] = None


class UserScoreUpdate(SQLModel):
    """User score update model"""
    course_points: Optional[int] = None
    exam_points: Optional[int] = None
    forum_points: Optional[int] = None
    bonus_points: Optional[int] = None
    activity_date: Optional[datetime] = None
    achievements: Optional[List[str]] = None
    statistics: Optional[Dict[str, Any]] = None


class ScoreTransactionBase(SQLModel):
    """Base score transaction model"""
    points: int
    score_type: ScoreType
    description: str = Field(max_length=255)
    reference_id: Optional[UUID] = None  # ID of related object
    reference_type: Optional[str] = Field(default=None, max_length=50)


class ScoreTransaction(ScoreTransactionBase, TenantMixin, TimestampMixin, table=True):
    """Score transaction table model"""
    __tablename__ = "score_transactions"
    
    transaction_id: UUID = Field(primary_key=True)
    user_id: UUID = Field(foreign_key="users.user_id", index=True)
    awarded_by: Optional[UUID] = Field(foreign_key="users.user_id", default=None)
    extra_data: Optional[Dict[str, Any]] = Field(default_factory=dict, sa_column=Column(JSONB))


class ScoreTransactionCreate(ScoreTransactionBase):
    """Score transaction creation model"""
    user_id: UUID
    awarded_by: Optional[UUID] = None
    metadata: Optional[Dict[str, Any]] = None


class ScoreTransactionResponse(ScoreTransactionBase):
    """Score transaction response model"""
    transaction_id: UUID
    user_id: UUID
    awarded_by: Optional[UUID] = None
    metadata: Dict[str, Any]
    created_at: datetime
    user_name: Optional[str] = None
    awarded_by_name: Optional[str] = None


class SystemMetricsBase(SQLModel):
    """Base system metrics model"""
    metric_type: MetricType
    metric_date: datetime
    value: float
    count: int = Field(default=1)


class SystemMetrics(SystemMetricsBase, TenantMixin, table=True):
    """System metrics table model"""
    __tablename__ = "system_metrics"
    
    metric_id: UUID = Field(primary_key=True)
    dimensions: Optional[Dict[str, Any]] = Field(default_factory=dict, sa_column=Column(JSONB))
    created_at: datetime = Field(default_factory=datetime.utcnow)


class SystemMetricsCreate(SystemMetricsBase):
    """System metrics creation model"""
    dimensions: Optional[Dict[str, Any]] = None


class AnalyticsReport(SQLModel):
    """Analytics report model"""
    report_type: str
    date_from: datetime
    date_to: datetime
    data: Dict[str, Any]
    generated_at: datetime = Field(default_factory=datetime.utcnow)


class UserAnalytics(SQLModel):
    """User analytics model"""
    user_id: UUID
    total_courses: int
    completed_courses: int
    in_progress_courses: int
    total_exams: int
    passed_exams: int
    average_score: float
    total_study_time_minutes: int
    forum_posts: int
    forum_replies: int
    current_streak: int
    longest_streak: int
    achievements_count: int
    rank: Optional[int] = None
    percentile: Optional[float] = None


class CourseAnalytics(SQLModel):
    """Course analytics model"""
    course_id: UUID
    total_enrollments: int
    active_enrollments: int
    completed_enrollments: int
    completion_rate: float
    average_completion_time_days: Optional[float] = None
    average_rating: Optional[float] = None
    total_ratings: int
    dropout_rate: float
    engagement_score: float
    popular_modules: List[Dict[str, Any]]
    difficulty_feedback: Dict[str, int]


class ExamAnalytics(SQLModel):
    """Exam analytics model"""
    exam_id: UUID
    total_submissions: int
    completed_submissions: int
    average_score: float
    pass_rate: float
    average_time_minutes: float
    question_statistics: List[Dict[str, Any]]
    score_distribution: Dict[str, int]
    difficulty_analysis: Dict[str, Any]
    improvement_suggestions: List[str]


class TenantAnalytics(SQLModel):
    """Tenant analytics model"""
    tenant_id: UUID
    total_users: int
    active_users_30d: int
    total_courses: int
    published_courses: int
    total_enrollments: int
    completed_courses: int
    total_exams: int
    exam_submissions: int
    forum_posts: int
    storage_used_gb: float
    monthly_active_users: List[Dict[str, Any]]
    course_completion_trends: List[Dict[str, Any]]
    user_engagement_metrics: Dict[str, Any]


class LeaderboardEntry(SQLModel):
    """Leaderboard entry model"""
    user_id: UUID
    user_name: str
    avatar_url: Optional[str] = None
    total_points: int
    rank: int
    courses_completed: int
    average_score: float
    current_streak: int
    achievements_count: int
    change_from_last_week: Optional[int] = None


class Leaderboard(SQLModel):
    """Leaderboard model"""
    leaderboard_type: str  # 'weekly', 'monthly', 'all_time', 'course_creators'
    period_start: datetime
    period_end: datetime
    entries: List[LeaderboardEntry]
    total_participants: int
    generated_at: datetime = Field(default_factory=datetime.utcnow)


class EngagementMetrics(SQLModel):
    """Engagement metrics model"""
    date: datetime
    daily_active_users: int
    weekly_active_users: int
    monthly_active_users: int
    average_session_duration_minutes: float
    page_views: int
    unique_visitors: int
    bounce_rate: float
    retention_rate: float


class LearningPathAnalytics(SQLModel):
    """Learning path analytics model"""
    path_id: UUID
    total_users: int
    completed_users: int
    completion_rate: float
    average_completion_time_days: float
    dropout_points: List[Dict[str, Any]]
    success_factors: List[str]
    improvement_areas: List[str]


class RealtimeMetrics(SQLModel):
    """Real-time metrics model"""
    active_users_now: int
    exams_in_progress: int
    recent_completions: int
    forum_activity_last_hour: int
    system_load: float
    response_time_ms: float
    error_rate: float
    timestamp: datetime = Field(default_factory=datetime.utcnow)


# Missing response models
class PlatformMetrics(SQLModel):
    """Platform metrics model"""
    total_users: int
    active_users: int
    new_users: int
    total_courses: int
    total_enrollments: int
    completed_courses: int
    total_exams: int
    passed_exams: int
    total_posts: int
    total_replies: int
    completion_rate: float
    pass_rate: float
    engagement_rate: float


class PlatformMetricsResponse(SQLModel):
    """Platform metrics response model"""
    time_range: str
    metrics: PlatformMetrics
    generated_at: datetime


class CourseAnalyticsResponse(SQLModel):
    """Course analytics response model"""
    course_title: str
    analytics: CourseAnalytics
    generated_at: datetime


class UserAnalyticsResponse(SQLModel):
    """User analytics response model"""
    user_name: str
    analytics: UserAnalytics
    generated_at: datetime


class LeaderboardResponse(SQLModel):
    """Leaderboard response model"""
    time_range: str
    metric_type: str
    entries: List[LeaderboardEntry]
    total_users: int
    generated_at: datetime


class AnalyticsReportCreate(SQLModel):
    """Analytics report creation model"""
    report_type: str
    date_from: datetime
    date_to: datetime


class AnalyticsReportResponse(SQLModel):
    """Analytics report response model"""
    report_type: str
    date_from: datetime
    date_to: datetime
    data: Dict[str, Any]
    generated_at: datetime


# Enums
class MetricType(str, Enum):
    """Metric type enumeration"""
    TOTAL_POINTS = "total_points"
    COURSE_POINTS = "course_points"
    EXAM_POINTS = "exam_points"
    FORUM_POINTS = "forum_points"
    CURRENT_STREAK = "current_streak"


class TimeRange(str, Enum):
    """Time range enumeration"""
    LAST_7_DAYS = "last_7_days"
    LAST_30_DAYS = "last_30_days"
    LAST_90_DAYS = "last_90_days"
    LAST_YEAR = "last_year"
    ALL_TIME = "all_time"


class ReportType(str, Enum):
    """Report type enumeration"""
    USER_ENGAGEMENT = "user_engagement"
    COURSE_PERFORMANCE = "course_performance"
    EXAM_ANALYTICS = "exam_analytics"
    PLATFORM_OVERVIEW = "platform_overview"
