# Test Configuration for Different Environments
# Source this file to configure test behavior for different scenarios

# =============================================================================
# DEVELOPMENT ENVIRONMENT
# =============================================================================

# Quick validation for daily development
export DEV_VALIDATE_ON_START=false
export DEV_QUICK_VALIDATE=true
export DEV_VERBOSE_TESTS=false
export DEV_SILENT_MODE=false
export DEV_CONTINUE_ON_ERROR=true

# =============================================================================
# STARTUP ENVIRONMENT  
# =============================================================================

# Validation when starting the development environment
export STARTUP_VALIDATE_ON_START=true
export STARTUP_QUICK_VALIDATE=true
export STARTUP_VERBOSE_TESTS=false
export STARTUP_SILENT_MODE=false
export STARTUP_CONTINUE_ON_ERROR=true

# =============================================================================
# CI/CD ENVIRONMENT
# =============================================================================

# Strict validation for CI/CD pipelines
export CI_VALIDATE_ON_START=true
export CI_QUICK_VALIDATE=false
export CI_VERBOSE_TESTS=true
export CI_SILENT_MODE=false
export CI_CONTINUE_ON_ERROR=false

# =============================================================================
# PRODUCTION DEPLOYMENT
# =============================================================================

# Comprehensive validation before production deployment
export PROD_VALIDATE_ON_START=true
export PROD_QUICK_VALIDATE=false
export PROD_VERBOSE_TESTS=true
export PROD_SILENT_MODE=false
export PROD_CONTINUE_ON_ERROR=false

# =============================================================================
# HELPER FUNCTIONS
# =============================================================================

# Function to set development mode
set_dev_mode() {
    export VALIDATE_ON_START=$DEV_VALIDATE_ON_START
    export QUICK_VALIDATE=$DEV_QUICK_VALIDATE
    export VERBOSE_TESTS=$DEV_VERBOSE_TESTS
    export SILENT_MODE=$DEV_SILENT_MODE
    export CONTINUE_ON_ERROR=$DEV_CONTINUE_ON_ERROR
    export VALIDATION_MODE="quick"
    echo "🔧 Development mode configured"
}

# Function to set startup mode
set_startup_mode() {
    export VALIDATE_ON_START=$STARTUP_VALIDATE_ON_START
    export QUICK_VALIDATE=$STARTUP_QUICK_VALIDATE
    export VERBOSE_TESTS=$STARTUP_VERBOSE_TESTS
    export SILENT_MODE=$STARTUP_SILENT_MODE
    export CONTINUE_ON_ERROR=$STARTUP_CONTINUE_ON_ERROR
    export VALIDATION_MODE="startup"
    echo "🚀 Startup mode configured"
}

# Function to set CI mode
set_ci_mode() {
    export VALIDATE_ON_START=$CI_VALIDATE_ON_START
    export QUICK_VALIDATE=$CI_QUICK_VALIDATE
    export VERBOSE_TESTS=$CI_VERBOSE_TESTS
    export SILENT_MODE=$CI_SILENT_MODE
    export CONTINUE_ON_ERROR=$CI_CONTINUE_ON_ERROR
    export VALIDATION_MODE="full"
    echo "🤖 CI/CD mode configured"
}

# Function to set production mode
set_prod_mode() {
    export VALIDATE_ON_START=$PROD_VALIDATE_ON_START
    export QUICK_VALIDATE=$PROD_QUICK_VALIDATE
    export VERBOSE_TESTS=$PROD_VERBOSE_TESTS
    export SILENT_MODE=$PROD_SILENT_MODE
    export CONTINUE_ON_ERROR=$PROD_CONTINUE_ON_ERROR
    export VALIDATION_MODE="deployment"
    echo "🏭 Production mode configured"
}

# Function to show current configuration
show_test_config() {
    echo "Current Test Configuration:"
    echo "  VALIDATE_ON_START: ${VALIDATE_ON_START:-not set}"
    echo "  QUICK_VALIDATE: ${QUICK_VALIDATE:-not set}"
    echo "  VERBOSE_TESTS: ${VERBOSE_TESTS:-not set}"
    echo "  SILENT_MODE: ${SILENT_MODE:-not set}"
    echo "  CONTINUE_ON_ERROR: ${CONTINUE_ON_ERROR:-not set}"
    echo "  VALIDATION_MODE: ${VALIDATION_MODE:-not set}"
}

# =============================================================================
# USAGE EXAMPLES
# =============================================================================

# Example usage in different scenarios:
#
# 1. Daily development:
#    source tests/test_config.env && set_dev_mode && make dev
#
# 2. First-time setup:
#    source tests/test_config.env && set_startup_mode && make dev-with-tests
#
# 3. CI/CD pipeline:
#    source tests/test_config.env && set_ci_mode && make ci-test
#
# 4. Production deployment:
#    source tests/test_config.env && set_prod_mode && make ci-validate

# =============================================================================
# DEFAULT CONFIGURATION
# =============================================================================

# Set development mode as default if no mode is specified
if [ -z "$VALIDATION_MODE" ]; then
    set_dev_mode
fi
