"""
Backend models base - compatibility import from core-api
"""

import sys
import os

# Add core-api to path
core_api_path = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'core-api')
sys.path.insert(0, core_api_path)

# Import from actual implementation
from app.models.base import *

# Create Base alias for SQLAlchemy compatibility
try:
    from app.models.base import BaseModel as Base
except ImportError:
    from sqlalchemy.ext.declarative import declarative_base
    Base = declarative_base()
